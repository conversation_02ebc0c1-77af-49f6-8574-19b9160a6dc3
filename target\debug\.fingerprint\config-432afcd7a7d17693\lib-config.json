{"rustc": 3926191382657067107, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17255432589167795725, "path": 8406906625069782941, "deps": [[1213098572879462490, "json5_rs", false, 13433835704707612958], [1965680986145237447, "yaml_rust2", false, 1932990705863014799], [2244620803250265856, "ron", false, 14501081934348007837], [6502365400774175331, "nom", false, 7198446798263691227], [6517602928339163454, "path<PERSON><PERSON>", false, 117801925635023875], [9689903380558560274, "serde", false, 15801814292485297685], [11946729385090170470, "async_trait", false, 15858958581867564283], [13475460906694513802, "convert_case", false, 10227037070397554461], [14618892375165583068, "ini", false, 10208954031731629374], [15367738274754116744, "serde_json", false, 9991360992155204765], [15609422047640926750, "toml", false, 11568872933771536445]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-432afcd7a7d17693\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}