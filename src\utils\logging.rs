use crate::prelude::*;
use std::path::Path;
use std::fs::OpenOptions;
use std::io::Write;
use log::LevelFilter;
use env_logger::{Builder, Target};

/// Initialize logging for Matrix AI
pub fn init_logging(log_level: &str, log_file: &Path) -> Result<()> {
    // Parse log level
    let level = match log_level.to_lowercase().as_str() {
        "error" => LevelFilter::Error,
        "warn" => LevelFilter::Warn,
        "info" => LevelFilter::Info,
        "debug" => LevelFilter::Debug,
        "trace" => LevelFilter::Trace,
        _ => LevelFilter::Info,
    };

    // Create log directory if it doesn't exist
    if let Some(parent) = log_file.parent() {
        std::fs::create_dir_all(parent)
            .context("Failed to create log directory")?;
    }

    // Initialize env_logger with custom format
    Builder::from_default_env()
        .target(Target::Stdout)
        .filter_level(level)
        .format(|buf, record| {
            writeln!(
                buf,
                "{} [{}] [{}:{}] - {}",
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f"),
                record.level(),
                record.file().unwrap_or("unknown"),
                record.line().unwrap_or(0),
                record.args()
            )
        })
        .init();

    // Also log to file
    setup_file_logging(log_file, level)?;

    info!("Logging initialized with level: {}", log_level);
    info!("Log file: {:?}", log_file);

    Ok(())
}

/// Setup file logging
fn setup_file_logging(log_file: &Path, level: LevelFilter) -> Result<()> {
    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(log_file)
        .context("Failed to open log file")?;

    // Note: This is a simplified file logging setup
    // In a production system, you might want to use a more sophisticated
    // logging framework like tracing or log4rs

    Ok(())
}

/// Log system information
pub fn log_system_info() {
    info!("=== Matrix AI System Information ===");
    info!("OS: {}", std::env::consts::OS);
    info!("Architecture: {}", std::env::consts::ARCH);
    info!("Rust version: {}", env!("CARGO_PKG_VERSION"));

    // Log memory information
    if let Ok(sys_info) = get_system_info() {
        info!("Total memory: {} MB", sys_info.total_memory / 1024 / 1024);
        info!("Available memory: {} MB", sys_info.available_memory / 1024 / 1024);
        info!("CPU cores: {}", sys_info.cpu_cores);
    }

    info!("=====================================");
}

/// System information structure
#[derive(Debug)]
pub struct SystemInfo {
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_cores: usize,
}

/// Get basic system information
pub fn get_system_info() -> Result<SystemInfo> {
    use sysinfo::{System, SystemExt, CpuExt};

    let mut sys = System::new_all();
    sys.refresh_all();

    Ok(SystemInfo {
        total_memory: sys.total_memory(),
        available_memory: sys.available_memory(),
        cpu_cores: sys.cpus().len(),
    })
}

/// Log performance metrics
pub fn log_performance_metrics(operation: &str, duration: std::time::Duration) {
    debug!(
        "Performance: {} completed in {:.2}ms",
        operation,
        duration.as_secs_f64() * 1000.0
    );
}

/// Create a performance timer
pub struct PerformanceTimer {
    operation: String,
    start_time: std::time::Instant,
}

impl PerformanceTimer {
    pub fn new(operation: &str) -> Self {
        Self {
            operation: operation.to_string(),
            start_time: std::time::Instant::now(),
        }
    }

    pub fn finish(self) {
        let duration = self.start_time.elapsed();
        log_performance_metrics(&self.operation, duration);
    }
}

/// Macro for easy performance timing
#[macro_export]
macro_rules! time_operation {
    ($operation:expr, $code:block) => {{
        let timer = $crate::utils::logging::PerformanceTimer::new($operation);
        let result = $code;
        timer.finish();
        result
    }};
}

/// Log error with context
pub fn log_error_with_context(error: &anyhow::Error, context: &str) {
    error!("Error in {}: {}", context, error);

    let mut source = error.source();
    let mut level = 1;

    while let Some(err) = source {
        error!("  Caused by ({}): {}", level, err);
        source = err.source();
        level += 1;
    }
}

/// Log startup banner
pub fn log_startup_banner() {
    info!("╔══════════════════════════════════════╗");
    info!("║            MATRIX AI v0.1.0          ║");
    info!("║     Autonomous Offline Assistant     ║");
    info!("║                                      ║");
    info!("║  🧠 AI • 🗣️ Voice • ⚙️ Automation    ║");
    info!("╚══════════════════════════════════════╝");
}

/// Log shutdown message
pub fn log_shutdown() {
    info!("╔══════════════════════════════════════╗");
    info!("║         Matrix AI Shutdown           ║");
    info!("║        Thank you for using           ║");
    info!("║         Matrix AI v0.1.0             ║");
    info!("╚══════════════════════════════════════╝");
}