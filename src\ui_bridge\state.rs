use crate::prelude::*;

/// Global state for Matrix AI UI
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MatrixState {
    pub is_listening: bool,
    pub is_speaking: bool,
    pub last_user_input: Option<String>,
    pub last_response: Option<String>,
    pub status: AssistantStatus,
    pub capabilities: Vec<String>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AssistantStatus {
    Initializing,
    Ready,
    Listening,
    Processing,
    Speaking,
    Error(String),
}

impl Default for MatrixState {
    fn default() -> Self {
        Self {
            is_listening: false,
            is_speaking: false,
            last_user_input: None,
            last_response: None,
            status: AssistantStatus::Initializing,
            capabilities: Vec::new(),
        }
    }
}

impl MatrixState {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn set_status(&mut self, status: AssistantStatus) {
        self.status = status;
    }

    pub fn set_listening(&mut self, listening: bool) {
        self.is_listening = listening;
        if listening {
            self.status = AssistantStatus::Listening;
        }
    }

    pub fn set_speaking(&mut self, speaking: bool) {
        self.is_speaking = speaking;
        if speaking {
            self.status = AssistantStatus::Speaking;
        }
    }

    pub fn set_user_input(&mut self, input: String) {
        self.last_user_input = Some(input);
        self.status = AssistantStatus::Processing;
    }

    pub fn set_response(&mut self, response: String) {
        self.last_response = Some(response);
    }

    pub fn set_capabilities(&mut self, capabilities: Vec<String>) {
        self.capabilities = capabilities;
    }
}