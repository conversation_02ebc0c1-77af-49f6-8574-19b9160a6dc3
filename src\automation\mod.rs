pub mod apps;
pub mod browser;
pub mod email;
pub mod system;
pub mod whatsapp;

use crate::prelude::*;
use crate::config::AutomationConfig;

/// Result of an automation operation
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum AutomationResult {
    Success(String),
    Error(String),
    Info(String),
}

/// Main automation engine that coordinates all automation modules
pub struct AutomationEngine {
    config: AutomationConfig,
    apps_handler: apps::AppsHand<PERSON>,
    browser_handler: browser::BrowserHandler,
    email_handler: email::EmailHandler,
    system_handler: system::SystemHandler,
    whatsapp_handler: whatsapp::WhatsAppHandler,
}

impl AutomationEngine {
    /// Create a new automation engine
    pub async fn new(config: &AutomationConfig) -> Result<Self> {
        info!("Initializing Automation Engine...");

        let apps_handler = apps::AppsHandler::new().await?;
        let browser_handler = browser::BrowserHandler::new(&config.browser).await?;
        let email_handler = email::EmailHandler::new(&config.email).await?;
        let system_handler = system::SystemHandler::new(&config.system).await?;
        let whatsapp_handler = whatsapp::WhatsAppHandler::new(&config.whatsapp).await?;

        info!("Automation Engine initialized successfully");

        Ok(Self {
            config: config.clone(),
            apps_handler,
            browser_handler,
            email_handler,
            system_handler,
            whatsapp_handler,
        })
    }

    /// Execute an automation based on the parsed intent
    pub async fn execute(&self, intent: &Intent) -> Result<AutomationResult> {
        debug!("Executing automation for intent: {:?}", intent);

        let result = match intent {
            Intent::OpenApp { app_name, details } => {
                self.apps_handler.open_app(app_name, details.as_deref()).await
            }

            Intent::SendMessage { platform, target, message } => {
                match platform {
                    crate::ai::parser::MessagePlatform::WhatsApp => {
                        self.whatsapp_handler.send_message(target, message).await
                    }
                    crate::ai::parser::MessagePlatform::Email => {
                        self.email_handler.send_email(target, "Message from Matrix AI", message).await
                    }
                    crate::ai::parser::MessagePlatform::SMS => {
                        // SMS not implemented yet
                        Ok(AutomationResult::Error("SMS not supported yet".to_string()))
                    }
                }
            }

            Intent::SearchWeb { query, search_engine } => {
                self.browser_handler.search_web(query, search_engine.as_deref()).await
            }

            Intent::SystemControl { action, parameters } => {
                self.system_handler.execute_system_action(action, parameters.as_deref()).await
            }

            Intent::BrowserControl { action, url, text } => {
                self.browser_handler.execute_browser_action(action, url.as_deref(), text.as_deref()).await
            }

            Intent::EmailAction { action, recipient, subject, body } => {
                self.email_handler.execute_email_action(action, recipient.as_deref(), subject.as_deref(), body.as_deref()).await
            }

            Intent::Unknown { original_text, suggested_response } => {
                warn!("Unknown intent: {}", original_text);
                Ok(AutomationResult::Info(suggested_response.clone()))
            }
        };

        match &result {
            Ok(AutomationResult::Success(msg)) => info!("Automation successful: {}", msg),
            Ok(AutomationResult::Error(msg)) => warn!("Automation error: {}", msg),
            Ok(AutomationResult::Info(msg)) => info!("Automation info: {}", msg),
            Err(e) => error!("Automation failed: {}", e),
        }

        result
    }

    /// Test all automation modules
    pub async fn test_all(&self) -> Result<()> {
        info!("Testing all automation modules...");

        // Test each module
        if let Err(e) = self.apps_handler.test().await {
            warn!("Apps handler test failed: {}", e);
        }

        if let Err(e) = self.browser_handler.test().await {
            warn!("Browser handler test failed: {}", e);
        }

        if let Err(e) = self.email_handler.test().await {
            warn!("Email handler test failed: {}", e);
        }

        if let Err(e) = self.system_handler.test().await {
            warn!("System handler test failed: {}", e);
        }

        if let Err(e) = self.whatsapp_handler.test().await {
            warn!("WhatsApp handler test failed: {}", e);
        }

        info!("Automation module testing completed");
        Ok(())
    }

    /// Get automation capabilities
    pub fn get_capabilities(&self) -> Vec<String> {
        vec![
            "Open applications".to_string(),
            "Send WhatsApp messages".to_string(),
            "Send emails".to_string(),
            "Web browsing and search".to_string(),
            "System control (volume, screenshots, etc.)".to_string(),
            "Browser automation".to_string(),
        ]
    }

    /// Shutdown the automation engine
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down Automation Engine...");

        // Shutdown all handlers
        self.apps_handler.shutdown().await?;
        self.browser_handler.shutdown().await?;
        self.email_handler.shutdown().await?;
        self.system_handler.shutdown().await?;
        self.whatsapp_handler.shutdown().await?;

        info!("Automation Engine shutdown complete");
        Ok(())
    }
}

// Re-export commonly used types
pub use apps::AppsHandler;
pub use browser::BrowserHandler;
pub use email::EmailHandler;
pub use system::SystemHandler;
pub use whatsapp::WhatsAppHandler;

// Re-export Intent and related types for convenience
pub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};