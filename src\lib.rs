pub mod config;
pub mod ai;
pub mod voice;
pub mod automation;
pub mod utils;
pub mod ui_bridge;

// Re-export commonly used types
pub use config::{MatrixConfig, load_config, save_config, ensure_directories};

// Error types
pub type Result<T> = anyhow::Result<T>;

// Common imports for the entire crate
pub mod prelude {
    pub use crate::config::*;
    pub use crate::Result;
    pub use anyhow::{anyhow, Context};
    pub use log::{debug, error, info, warn};
    pub use serde::{Deserialize, Serialize};
    pub use std::sync::Arc;
    pub use tokio::sync::{Mutex, RwLock};
}