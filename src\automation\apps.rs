use crate::prelude::*;
use super::AutomationResult;
use std::process::Command;
use std::collections::HashMap;

/// Handler for application launching and management
pub struct AppsHandler {
    app_mappings: HashMap<String, String>,
}

impl AppsHandler {
    /// Create a new apps handler
    pub async fn new() -> Result<Self> {
        info!("Initializing Apps Handler...");

        let mut app_mappings = HashMap::new();

        // Common application mappings for different platforms
        #[cfg(target_os = "windows")]
        {
            app_mappings.insert("chrome".to_string(), "chrome".to_string());
            app_mappings.insert("firefox".to_string(), "firefox".to_string());
            app_mappings.insert("edge".to_string(), "msedge".to_string());
            app_mappings.insert("notepad".to_string(), "notepad".to_string());
            app_mappings.insert("calculator".to_string(), "calc".to_string());
            app_mappings.insert("paint".to_string(), "mspaint".to_string());
            app_mappings.insert("explorer".to_string(), "explorer".to_string());
            app_mappings.insert("cmd".to_string(), "cmd".to_string());
            app_mappings.insert("powershell".to_string(), "powershell".to_string());
            app_mappings.insert("vscode".to_string(), "code".to_string());
            app_mappings.insert("visual studio code".to_string(), "code".to_string());
        }

        #[cfg(target_os = "macos")]
        {
            app_mappings.insert("chrome".to_string(), "open -a 'Google Chrome'".to_string());
            app_mappings.insert("firefox".to_string(), "open -a Firefox".to_string());
            app_mappings.insert("safari".to_string(), "open -a Safari".to_string());
            app_mappings.insert("finder".to_string(), "open -a Finder".to_string());
            app_mappings.insert("terminal".to_string(), "open -a Terminal".to_string());
            app_mappings.insert("vscode".to_string(), "open -a 'Visual Studio Code'".to_string());
            app_mappings.insert("visual studio code".to_string(), "open -a 'Visual Studio Code'".to_string());
        }

        #[cfg(target_os = "linux")]
        {
            app_mappings.insert("chrome".to_string(), "google-chrome".to_string());
            app_mappings.insert("firefox".to_string(), "firefox".to_string());
            app_mappings.insert("terminal".to_string(), "gnome-terminal".to_string());
            app_mappings.insert("files".to_string(), "nautilus".to_string());
            app_mappings.insert("vscode".to_string(), "code".to_string());
            app_mappings.insert("visual studio code".to_string(), "code".to_string());
        }

        info!("Apps Handler initialized with {} app mappings", app_mappings.len());

        Ok(Self { app_mappings })
    }

    /// Open an application by name
    pub async fn open_app(&self, app_name: &str, details: Option<&str>) -> Result<AutomationResult> {
        let app_name_lower = app_name.to_lowercase();

        info!("Attempting to open app: {}", app_name);

        // Try to find the app in our mappings
        if let Some(command) = self.app_mappings.get(&app_name_lower) {
            self.execute_app_command(command, details).await
        } else {
            // Try to open the app directly
            self.execute_app_command(&app_name_lower, details).await
        }
    }

    /// Execute an application command
    async fn execute_app_command(&self, command: &str, details: Option<&str>) -> Result<AutomationResult> {
        debug!("Executing app command: {}", command);

        let result = if command.contains("open -a") {
            // macOS style command
            self.execute_macos_command(command, details).await
        } else {
            // Windows/Linux style command
            self.execute_standard_command(command, details).await
        };

        match result {
            Ok(_) => {
                let message = format!("Successfully opened {}", command);
                info!("{}", message);
                Ok(AutomationResult::Success(message))
            }
            Err(e) => {
                let error_msg = format!("Failed to open {}: {}", command, e);
                warn!("{}", error_msg);
                Ok(AutomationResult::Error(error_msg))
            }
        }
    }

    /// Execute macOS style command
    async fn execute_macos_command(&self, command: &str, _details: Option<&str>) -> Result<()> {
        let parts: Vec<&str> = command.split_whitespace().collect();

        let output = Command::new("open")
            .args(&parts[1..])
            .output()
            .context("Failed to execute macOS open command")?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("Command failed: {}", error));
        }

        Ok(())
    }

    /// Execute standard command (Windows/Linux)
    async fn execute_standard_command(&self, command: &str, details: Option<&str>) -> Result<()> {
        let mut cmd = if cfg!(target_os = "windows") {
            let mut c = Command::new("cmd");
            c.args(&["/C", command]);
            c
        } else {
            let mut c = Command::new("sh");
            c.args(&["-c", command]);
            c
        };

        // Add details as arguments if provided
        if let Some(args) = details {
            cmd.arg(args);
        }

        let output = cmd.output()
            .context("Failed to execute command")?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("Command failed: {}", error));
        }

        Ok(())
    }

    /// List available applications
    pub fn list_available_apps(&self) -> Vec<String> {
        self.app_mappings.keys().cloned().collect()
    }

    /// Add a custom app mapping
    pub fn add_app_mapping(&mut self, name: String, command: String) {
        debug!("Added app mapping: {} -> {}", name, command);
        self.app_mappings.insert(name.to_lowercase(), command);
    }

    /// Remove an app mapping
    pub fn remove_app_mapping(&mut self, name: &str) {
        self.app_mappings.remove(&name.to_lowercase());
        debug!("Removed app mapping: {}", name);
    }

    /// Check if an app is available
    pub fn is_app_available(&self, app_name: &str) -> bool {
        self.app_mappings.contains_key(&app_name.to_lowercase())
    }

    /// Get the command for an app
    pub fn get_app_command(&self, app_name: &str) -> Option<&String> {
        self.app_mappings.get(&app_name.to_lowercase())
    }

    /// Test the apps handler
    pub async fn test(&self) -> Result<()> {
        info!("Testing Apps Handler...");

        // Test with a safe command that should work on all platforms
        let test_result = if cfg!(target_os = "windows") {
            self.execute_standard_command("echo", Some("test")).await
        } else {
            self.execute_standard_command("echo test", None).await
        };

        match test_result {
            Ok(_) => {
                info!("Apps Handler test successful");
                Ok(())
            }
            Err(e) => {
                warn!("Apps Handler test failed: {}", e);
                Err(e)
            }
        }
    }

    /// Shutdown the apps handler
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down Apps Handler...");
        // No cleanup needed for apps handler
        Ok(())
    }
}