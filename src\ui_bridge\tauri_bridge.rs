use crate::prelude::*;
use super::MatrixState;

/// Tauri bridge for UI communication (stub implementation)
pub struct TauriBridge {
    state: Arc<RwLock<MatrixState>>,
}

impl TauriBridge {
    pub fn new() -> Self {
        Self {
            state: Arc::new(RwLock::new(MatrixState::new())),
        }
    }

    pub async fn get_state(&self) -> MatrixState {
        self.state.read().await.clone()
    }

    pub async fn update_state<F>(&self, updater: F)
    where
        F: FnOnce(&mut MatrixState),
    {
        let mut state = self.state.write().await;
        updater(&mut state);
    }

    pub async fn emit_event(&self, event: &str, data: serde_json::Value) {
        debug!("Emitting UI event: {} with data: {:?}", event, data);
        // In a real implementation, this would emit to the Tauri frontend
    }
}

impl Default for TauriBridge {
    fn default() -> Self {
        Self::new()
    }
}