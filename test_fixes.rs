// Test file to verify our fixes work
use std::collections::HashMap;

// Test the logging fix
fn test_logging_fix() {
    println!("Rust version: {}", env!("CARGO_PKG_VERSION"));
}

// Test the sysinfo fix
fn test_sysinfo_fix() {
    use sysinfo::{System, SystemExt, CpuExt};
    
    let mut sys = System::new_all();
    sys.refresh_all();
    
    println!("CPU cores: {}", sys.cpus().len());
    println!("Total memory: {} KB", sys.total_memory());
}

// Test the chrono fix
fn test_chrono_fix() {
    use chrono::{Local, Timelike};
    
    let now = Local::now();
    let hour = now.hour();
    println!("Current hour: {}", hour);
}

// Test the borrow checker fix
fn test_borrow_checker_fix() {
    let mut mappings: HashMap<String, String> = HashMap::new();
    let name = "test".to_string();
    let command = "test_command".to_string();
    
    // This should work without borrow checker errors
    println!("Adding mapping: {} -> {}", name, command);
    mappings.insert(name.to_lowercase(), command);
}

fn main() {
    println!("Testing Matrix AI fixes...");
    
    test_logging_fix();
    test_sysinfo_fix();
    test_chrono_fix();
    test_borrow_checker_fix();
    
    println!("All tests completed!");
}
