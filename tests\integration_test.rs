use matrix_ai::prelude::*;
use matrix_ai::{
    config::{MatrixConfig, load_config},
    ai::{llm::LlmClient, parser::IntentParser},
    automation::AutomationEngine,
};

#[tokio::test]
async fn test_config_loading() {
    let config = MatrixConfig::default();
    assert_eq!(config.ai.model_name, "tinyllama");
    assert_eq!(config.ai.ollama_url, "http://localhost:11434");
}

#[tokio::test]
async fn test_intent_parser() {
    let parser = IntentParser::new();
    
    let test_response = r#"
INTENT: open_app
TARGET: chrome
DETAILS: browser
RESPONSE: Opening Chrome browser for you.
"#;
    
    let intent = parser.parse(test_response).unwrap();
    match intent {
        matrix_ai::ai::parser::Intent::OpenApp { app_name, .. } => {
            assert_eq!(app_name, "chrome");
        }
        _ => panic!("Expected OpenApp intent"),
    }
}

#[tokio::test]
async fn test_natural_language_parsing() {
    let parser = IntentParser::new();
    let intent = parser.parse_natural_language("open chrome browser");
    
    match intent {
        matrix_ai::ai::parser::Intent::OpenApp { app_name, .. } => {
            assert_eq!(app_name, "chrome");
        }
        _ => panic!("Expected OpenApp intent"),
    }
}

#[tokio::test]
async fn test_automation_engine_initialization() {
    let config = MatrixConfig::default();
    
    // This test might fail if external dependencies are not available
    // but it tests the basic initialization logic
    match AutomationEngine::new(&config.automation).await {
        Ok(_) => println!("Automation engine initialized successfully"),
        Err(e) => println!("Automation engine initialization failed (expected): {}", e),
    }
}

#[tokio::test]
async fn test_configuration_serialization() {
    let config = MatrixConfig::default();
    
    // Test JSON serialization
    let json_str = serde_json::to_string_pretty(&config).unwrap();
    assert!(json_str.contains("tinyllama"));
    
    // Test deserialization
    let deserialized: MatrixConfig = serde_json::from_str(&json_str).unwrap();
    assert_eq!(deserialized.ai.model_name, config.ai.model_name);
}

#[cfg(test)]
mod unit_tests {
    use super::*;
    use matrix_ai::utils::{time::TimeUtils, audio::AudioUtils};
    
    #[test]
    fn test_time_utils() {
        let now = TimeUtils::now_utc();
        let timestamp = TimeUtils::unix_timestamp();
        assert!(timestamp > 0);
        
        let formatted = TimeUtils::format_for_log(now);
        assert!(formatted.contains("UTC"));
    }
    
    #[test]
    fn test_audio_utils() {
        let samples = vec![0.1, 0.2, -0.1, -0.2, 0.0];
        let level = AudioUtils::calculate_audio_level(&samples);
        assert!(level > 0.0);
        assert!(level < 1.0);
    }
    
    #[test]
    fn test_voice_activity_detection() {
        // Test with silence
        let silence = vec![0.0; 1000];
        let is_voice = AudioUtils::detect_voice_activity(&silence, 0.01, 0.5);
        assert!(!is_voice);
        
        // Test with noise
        let noise: Vec<f32> = (0..1000).map(|i| (i as f32 * 0.1).sin() * 0.1).collect();
        let is_voice = AudioUtils::detect_voice_activity(&noise, 0.01, 0.5);
        assert!(is_voice);
    }
}
