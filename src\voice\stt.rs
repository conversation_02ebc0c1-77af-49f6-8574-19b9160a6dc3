use crate::prelude::*;
use crate::config::SttConfig;
use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use std::sync::mpsc;
use std::process::Command;
use std::fs::File;
use std::io::Write;
use hound::{WavWriter, WavSpec};
use std::path::PathBuf;

/// Speech-to-Text engine using Whisper.cpp
pub struct SttEngine {
    config: SttConfig,
    audio_host: Host,
    input_device: Device,
    stream_config: StreamConfig,
    is_listening: bool,
    whisper_path: PathBuf,
}

impl SttEngine {
    /// Create a new STT engine
    pub async fn new(config: &SttConfig) -> Result<Self> {
        info!("Initializing Speech-to-Text engine...");

        let audio_host = cpal::default_host();
        let input_device = audio_host
            .default_input_device()
            .ok_or_else(|| anyhow!("No input device available"))?;

        info!("Using input device: {}", input_device.name().unwrap_or("Unknown".to_string()));

        let supported_config = input_device
            .default_input_config()
            .context("Failed to get default input config")?;

        let stream_config = StreamConfig {
            channels: 1, // Mono audio for speech recognition
            sample_rate: cpal::SampleRate(config.sample_rate),
            buffer_size: cpal::BufferSize::Default,
        };

        // Find Whisper.cpp executable
        let whisper_path = Self::find_whisper_executable()?;

        info!("Found Whisper.cpp at: {:?}", whisper_path);

        Ok(Self {
            config: config.clone(),
            audio_host,
            input_device,
            stream_config,
            is_listening: false,
            whisper_path,
        })
    }

    /// Listen for speech and return transcribed text
    pub async fn listen_for_speech(&mut self) -> Result<String> {
        if self.is_listening {
            return Ok(String::new()); // Already listening
        }

        debug!("Starting to listen for speech...");

        // Record audio for a fixed duration
        let audio_data = self.record_audio().await?;

        if audio_data.is_empty() {
            return Ok(String::new());
        }

        // Save audio to temporary file
        let temp_audio_path = self.save_audio_to_file(&audio_data)?;

        // Transcribe using Whisper.cpp
        let transcription = self.transcribe_audio(&temp_audio_path).await?;

        // Clean up temporary file
        if let Err(e) = std::fs::remove_file(&temp_audio_path) {
            warn!("Failed to remove temporary audio file: {}", e);
        }

        debug!("Transcription result: {}", transcription);
        Ok(transcription)
    }

    /// Record audio from microphone
    async fn record_audio(&mut self) -> Result<Vec<f32>> {
        let (sender, receiver) = mpsc::channel();
        let mut audio_data = Vec::new();

        self.is_listening = true;

        let stream = self.input_device.build_input_stream(
            &self.stream_config,
            move |data: &[f32], _: &cpal::InputCallbackInfo| {
                if let Err(e) = sender.send(data.to_vec()) {
                    error!("Failed to send audio data: {}", e);
                }
            },
            |err| {
                error!("Audio stream error: {}", err);
            },
            None,
        )?;

        stream.play()?;

        // Record for a specific duration (e.g., 3 seconds)
        let recording_duration = tokio::time::Duration::from_millis(self.config.chunk_duration_ms);
        let start_time = tokio::time::Instant::now();

        while start_time.elapsed() < recording_duration {
            if let Ok(chunk) = receiver.try_recv() {
                audio_data.extend(chunk);
            }
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }

        drop(stream);
        self.is_listening = false;

        // Check if we have enough audio data (basic voice activity detection)
        let rms = Self::calculate_rms(&audio_data);
        if rms < 0.01 { // Threshold for silence
            return Ok(Vec::new());
        }

        Ok(audio_data)
    }

    /// Calculate RMS (Root Mean Square) for basic voice activity detection
    fn calculate_rms(audio_data: &[f32]) -> f32 {
        if audio_data.is_empty() {
            return 0.0;
        }

        let sum_squares: f32 = audio_data.iter().map(|&x| x * x).sum();
        (sum_squares / audio_data.len() as f32).sqrt()
    }

    /// Save audio data to WAV file
    fn save_audio_to_file(&self, audio_data: &[f32]) -> Result<PathBuf> {
        let temp_dir = std::env::temp_dir();
        let temp_file = temp_dir.join(format!("matrix_ai_audio_{}.wav", uuid::Uuid::new_v4()));

        let spec = WavSpec {
            channels: 1,
            sample_rate: self.config.sample_rate,
            bits_per_sample: 16,
            sample_format: hound::SampleFormat::Int,
        };

        let mut writer = WavWriter::create(&temp_file, spec)
            .context("Failed to create WAV writer")?;

        for &sample in audio_data {
            let sample_i16 = (sample * i16::MAX as f32) as i16;
            writer.write_sample(sample_i16)
                .context("Failed to write audio sample")?;
        }

        writer.finalize()
            .context("Failed to finalize WAV file")?;

        Ok(temp_file)
    }

    /// Transcribe audio using Whisper.cpp
    async fn transcribe_audio(&self, audio_path: &PathBuf) -> Result<String> {
        debug!("Transcribing audio file: {:?}", audio_path);

        let output = Command::new(&self.whisper_path)
            .arg("-m").arg(&self.config.model_path)
            .arg("-f").arg(audio_path)
            .arg("-l").arg(&self.config.language)
            .arg("--output-txt")
            .arg("--no-timestamps")
            .output()
            .context("Failed to execute Whisper.cpp")?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("Whisper.cpp failed: {}", error_msg));
        }

        let transcription = String::from_utf8_lossy(&output.stdout);
        let cleaned_transcription = transcription
            .lines()
            .filter(|line| !line.trim().is_empty())
            .collect::<Vec<_>>()
            .join(" ")
            .trim()
            .to_string();

        Ok(cleaned_transcription)
    }

    /// Find Whisper.cpp executable
    fn find_whisper_executable() -> Result<PathBuf> {
        // Try common locations for Whisper.cpp
        let possible_paths = vec![
            PathBuf::from("./whisper.cpp/main"),
            PathBuf::from("./whisper.cpp/main.exe"),
            PathBuf::from("whisper"),
            PathBuf::from("whisper.exe"),
            PathBuf::from("/usr/local/bin/whisper"),
            PathBuf::from("/usr/bin/whisper"),
        ];

        for path in possible_paths {
            if path.exists() {
                return Ok(path);
            }
        }

        // Try to find in PATH
        if let Ok(output) = Command::new("which").arg("whisper").output() {
            if output.status.success() {
                let path_str = String::from_utf8_lossy(&output.stdout).trim().to_string();
                return Ok(PathBuf::from(path_str));
            }
        }

        Err(anyhow!("Whisper.cpp executable not found. Please ensure it's installed and in PATH."))
    }

    /// Check if Whisper model exists
    pub fn check_model(&self) -> Result<()> {
        if !self.config.model_path.exists() {
            return Err(anyhow!(
                "Whisper model not found at: {:?}. Please download the model file.",
                self.config.model_path
            ));
        }
        Ok(())
    }

    /// Test the STT engine
    pub async fn test(&mut self) -> Result<()> {
        info!("Testing Speech-to-Text engine...");

        self.check_model()?;

        // Test audio recording
        let test_audio = self.record_audio().await?;
        if test_audio.is_empty() {
            warn!("No audio detected during test");
        } else {
            info!("Audio recording test successful");
        }

        Ok(())
    }

    /// Shutdown the STT engine
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down Speech-to-Text engine...");
        self.is_listening = false;
        Ok(())
    }
}