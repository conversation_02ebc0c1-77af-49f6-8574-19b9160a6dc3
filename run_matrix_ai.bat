@echo off
setlocal enabledelayedexpansion

:: Matrix AI Launcher
:: Comprehensive batch file to run the Matrix AI system

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🤖 MATRIX AI 🤖                       ║
echo ║                    Intelligent Automation System             ║
echo ║                                                              ║
echo ║                      Starting System...                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Set up environment
set "MATRIX_AI_DIR=%~dp0"
cd /d "%MATRIX_AI_DIR%"

:: Add Rust to PATH if available
if exist "%USERPROFILE%\.cargo\bin\cargo.exe" (
    set "PATH=%USERPROFILE%\.cargo\bin;%PATH%"
    echo ✅ Rust toolchain found
) else (
    echo ⚠️  Rust toolchain not found in user directory
    echo    Checking system installation...
    where cargo >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ System Rust installation found
    ) else (
        echo ❌ Rust not found. Will run Python components only.
        goto :python_only
    )
)

:: Check if this is first run or if we should rebuild
set "FORCE_BUILD=0"
if "%1"=="--build" set "FORCE_BUILD=1"
if "%1"=="-b" set "FORCE_BUILD=1"
if not exist "target\release\matrix-ai.exe" set "FORCE_BUILD=1"

echo.
echo 🔍 Checking system status...

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python available

:: Check Python dependencies
echo 🔧 Checking Python dependencies...
python -c "import selenium, pyautogui, requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Installing Python dependencies...
    pip install -r scripts\requirements.txt
    if !errorlevel! neq 0 (
        echo ❌ Failed to install Python dependencies
        pause
        exit /b 1
    )
)
echo ✅ Python dependencies ready

:: Build Rust components if needed
if %FORCE_BUILD% equ 1 (
    echo.
    echo 🔨 Building Matrix AI (Rust components)...
    echo    This may take a few minutes on first run...
    
    cargo check
    if !errorlevel! neq 0 (
        echo ❌ Rust compilation check failed
        echo    Running Python components only...
        goto :python_only
    )
    
    cargo build --release
    if !errorlevel! neq 0 (
        echo ❌ Rust build failed
        echo    Running Python components only...
        goto :python_only
    )
    
    echo ✅ Rust build completed successfully
)

:: Check if Rust executable exists
if exist "target\release\matrix-ai.exe" (
    echo.
    echo 🚀 Starting Matrix AI (Full System - Rust + Python)...
    echo.
    
    :: Run the full Rust application
    target\release\matrix-ai.exe %*
    set "EXIT_CODE=!errorlevel!"
    
    if !EXIT_CODE! neq 0 (
        echo.
        echo ⚠️  Matrix AI exited with code !EXIT_CODE!
        echo    Falling back to Python components...
        goto :python_only
    )
    
    goto :end
) else (
    echo ⚠️  Rust executable not found, running Python components...
    goto :python_only
)

:python_only
echo.
echo 🐍 Starting Matrix AI (Python Components Only)...
echo.

:: Show available Python scripts
echo Available Matrix AI Python tools:
echo.
echo 1. 📱 WhatsApp Automation
echo 2. 🧪 System Demo
echo 3. 🔧 Component Tests
echo 4. ❓ Help
echo.

if "%1"=="--demo" goto :run_demo
if "%1"=="-d" goto :run_demo
if "%1"=="--test" goto :run_test
if "%1"=="-t" goto :run_test
if "%1"=="--whatsapp" goto :run_whatsapp
if "%1"=="-w" goto :run_whatsapp
if "%1"=="--help" goto :show_help
if "%1"=="-h" goto :show_help

:: Interactive menu if no arguments
set /p "choice=Enter your choice (1-4): "

if "%choice%"=="1" goto :run_whatsapp
if "%choice%"=="2" goto :run_demo
if "%choice%"=="3" goto :run_test
if "%choice%"=="4" goto :show_help

echo Invalid choice. Running demo...

:run_demo
echo 🎬 Running Matrix AI Demo...
python demo_matrix_ai.py
goto :end

:run_test
echo 🧪 Running Component Tests...
python test_python_components.py
goto :end

:run_whatsapp
echo 📱 WhatsApp Automation
echo.
if "%2"=="" (
    echo Usage: run_matrix_ai.bat --whatsapp [contact] [message]
    echo Example: run_matrix_ai.bat --whatsapp "John Doe" "Hello from Matrix AI!"
    echo.
    echo Or run with --help for more options:
    python scripts\whatsapp.py --help
) else (
    python scripts\whatsapp.py --contact "%2" --message "%3" %4 %5 %6 %7 %8 %9
)
goto :end

:show_help
echo.
echo 🤖 Matrix AI - Usage Instructions
echo ═══════════════════════════════════
echo.
echo Basic Usage:
echo   run_matrix_ai.bat                    - Interactive menu
echo   run_matrix_ai.bat --demo             - Run system demo
echo   run_matrix_ai.bat --test             - Run component tests
echo   run_matrix_ai.bat --build            - Force rebuild Rust components
echo.
echo WhatsApp Automation:
echo   run_matrix_ai.bat --whatsapp "Contact" "Message"
echo   run_matrix_ai.bat -w "John" "Hello!"
echo.
echo Advanced Options:
echo   run_matrix_ai.bat --help             - Show this help
echo   run_matrix_ai.bat -h                 - Show this help
echo.
echo Examples:
echo   run_matrix_ai.bat --demo
echo   run_matrix_ai.bat --whatsapp "Mom" "Hi from Matrix AI!"
echo   run_matrix_ai.bat --test
echo   run_matrix_ai.bat --build
echo.
echo For WhatsApp script options:
python scripts\whatsapp.py --help
goto :end

:end
echo.
echo ═══════════════════════════════════
echo 🤖 Matrix AI session completed
echo ═══════════════════════════════════
if "%1"=="" pause
exit /b 0
