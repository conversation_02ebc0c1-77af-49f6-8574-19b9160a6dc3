# 🤖 Matrix AI - Batch File Launchers

## 📁 Available Batch Files

### 🚀 **run_matrix_ai.bat** - Main Launcher
**The primary way to run Matrix AI with full functionality**

```batch
# Interactive menu
run_matrix_ai.bat

# Quick demo
run_matrix_ai.bat --demo

# Run tests
run_matrix_ai.bat --test

# Force rebuild
run_matrix_ai.bat --build

# WhatsApp automation
run_matrix_ai.bat --whatsapp "Contact" "Message"

# Show help
run_matrix_ai.bat --help
```

**Features:**
- ✅ Automatic environment detection
- ✅ Rust + Python integration
- ✅ Fallback to Python-only mode
- ✅ Interactive menu system
- ✅ Comprehensive error handling
- ✅ Build management

---

### ⚡ **quick_start.bat** - Instant Launch
**For immediate Matrix AI demo**

```batch
# Double-click or run from command line
quick_start.bat
```

**Features:**
- ✅ One-click startup
- ✅ Automatic system detection
- ✅ Runs full system or demo
- ✅ Perfect for first-time users

---

### 📱 **whatsapp.bat** - WhatsApp Automation
**Dedicated WhatsApp message sender**

```batch
# Send a message
whatsapp.bat "<PERSON>" "Hello from Matrix AI!"

# Show help
whatsapp.bat
```

**Features:**
- ✅ Direct WhatsApp automation
- ✅ Simple command-line interface
- ✅ Error handling and feedback
- ✅ Help system

---

### 🔨 **build_and_test.bat** - Development Tool
**Complete build and test pipeline**

```batch
# Build and test everything
build_and_test.bat
```

**Features:**
- ✅ Environment validation
- ✅ Python component testing
- ✅ Rust compilation and testing
- ✅ Build verification
- ✅ Comprehensive status reporting

---

## 🎯 **Quick Start Guide**

### **For First-Time Users:**
1. **Double-click `quick_start.bat`** - Instant demo
2. **Or run `run_matrix_ai.bat`** - Full interactive experience

### **For WhatsApp Automation:**
```batch
whatsapp.bat "Contact Name" "Your message here"
```

### **For Development:**
```batch
build_and_test.bat
```

---

## 🔧 **Command Reference**

### **Main Launcher Options:**
| Command | Description |
|---------|-------------|
| `run_matrix_ai.bat` | Interactive menu |
| `run_matrix_ai.bat --demo` | Run system demo |
| `run_matrix_ai.bat --test` | Run component tests |
| `run_matrix_ai.bat --build` | Force rebuild |
| `run_matrix_ai.bat --whatsapp "Contact" "Message"` | Send WhatsApp message |
| `run_matrix_ai.bat --help` | Show help |

### **Quick Commands:**
| File | Purpose |
|------|---------|
| `quick_start.bat` | Instant demo |
| `whatsapp.bat "Contact" "Message"` | Send WhatsApp |
| `build_and_test.bat` | Build & test |

---

## 🛠️ **System Requirements**

### **Automatic Detection:**
All batch files automatically detect and handle:
- ✅ **Rust installation** (Cargo toolchain)
- ✅ **Python installation** (3.7+)
- ✅ **Python dependencies** (auto-install)
- ✅ **Build status** (auto-rebuild if needed)

### **Fallback Behavior:**
- If Rust not available → Python-only mode
- If build fails → Python components only
- If dependencies missing → Auto-install attempt

---

## 🎨 **Usage Examples**

### **Basic Usage:**
```batch
# Start Matrix AI
run_matrix_ai.bat

# Quick demo
quick_start.bat

# Send WhatsApp message
whatsapp.bat "Mom" "Hi from Matrix AI!"
```

### **Advanced Usage:**
```batch
# Force rebuild and run
run_matrix_ai.bat --build

# Run comprehensive tests
run_matrix_ai.bat --test

# Build everything from scratch
build_and_test.bat
```

### **Development Workflow:**
```batch
# 1. Build and test
build_and_test.bat

# 2. Run system
run_matrix_ai.bat

# 3. Test WhatsApp
whatsapp.bat "Test Contact" "Test message"
```

---

## 🔍 **Troubleshooting**

### **Common Issues:**

**"Rust not found"**
- Install Rust from https://rustup.rs/
- Or use Python-only mode (automatic fallback)

**"Python not found"**
- Install Python 3.7+ from https://python.org/
- Ensure Python is in PATH

**"Dependencies missing"**
- Run `build_and_test.bat` to auto-install
- Or manually: `pip install -r scripts\requirements.txt`

**"Build failed"**
- Check Windows SDK installation
- Use `run_matrix_ai.bat --demo` for Python-only mode

### **Debug Mode:**
All batch files provide detailed status information and error messages.

---

## 🎉 **Success Indicators**

### **Working System:**
- ✅ `quick_start.bat` shows Matrix AI demo
- ✅ `whatsapp.bat` can send messages
- ✅ `build_and_test.bat` completes successfully
- ✅ `run_matrix_ai.bat` shows full functionality

### **Ready for Production:**
- ✅ All batch files execute without errors
- ✅ Both Python and Rust components working
- ✅ WhatsApp automation functional
- ✅ System tests passing

---

## 🚀 **Get Started Now!**

**Choose your preferred method:**

1. **🎬 Demo First:** Double-click `quick_start.bat`
2. **📱 WhatsApp Test:** `whatsapp.bat "Test" "Hello Matrix AI!"`
3. **🔧 Full Setup:** Run `build_and_test.bat`
4. **⚡ Interactive:** Run `run_matrix_ai.bat`

**Matrix AI is ready to automate your world! 🤖✨**
