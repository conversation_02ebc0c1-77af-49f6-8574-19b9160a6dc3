use crate::prelude::*;
use cpal::{<PERSON><PERSON>, Host, SupportedStreamConfig};
use cpal::traits::{DeviceTrait, HostTrait};

/// Audio device information
#[derive(Debug, Clone)]
pub struct AudioDeviceInfo {
    pub name: String,
    pub is_default: bool,
    pub supported_configs: Vec<String>,
}

/// Audio utilities for Matrix AI
pub struct AudioUtils;

impl AudioUtils {
    /// List all available input devices
    pub fn list_input_devices() -> Result<Vec<AudioDeviceInfo>> {
        let host = cpal::default_host();
        let mut devices = Vec::new();

        let default_device = host.default_input_device();
        let default_name = default_device
            .as_ref()
            .and_then(|d| d.name().ok())
            .unwrap_or_default();

        for device in host.input_devices()? {
            let name = device.name().unwrap_or("Unknown Device".to_string());
            let is_default = name == default_name;

            let supported_configs = Self::get_supported_configs(&device)?;

            devices.push(AudioDeviceInfo {
                name,
                is_default,
                supported_configs,
            });
        }

        Ok(devices)
    }

    /// List all available output devices
    pub fn list_output_devices() -> Result<Vec<AudioDeviceInfo>> {
        let host = cpal::default_host();
        let mut devices = Vec::new();

        let default_device = host.default_output_device();
        let default_name = default_device
            .as_ref()
            .and_then(|d| d.name().ok())
            .unwrap_or_default();

        for device in host.output_devices()? {
            let name = device.name().unwrap_or("Unknown Device".to_string());
            let is_default = name == default_name;

            let supported_configs = Self::get_supported_configs(&device)?;

            devices.push(AudioDeviceInfo {
                name,
                is_default,
                supported_configs,
            });
        }

        Ok(devices)
    }

    /// Get supported configurations for a device
    fn get_supported_configs(device: &Device) -> Result<Vec<String>> {
        let mut configs = Vec::new();

        if let Ok(supported_configs) = device.supported_input_configs() {
            for config in supported_configs {
                configs.push(format!(
                    "{}ch {}Hz-{}Hz",
                    config.channels(),
                    config.min_sample_rate().0,
                    config.max_sample_rate().0
                ));
            }
        }

        Ok(configs)
    }

    /// Find input device by name
    pub fn find_input_device(name: &str) -> Result<Device> {
        let host = cpal::default_host();

        for device in host.input_devices()? {
            if let Ok(device_name) = device.name() {
                if device_name.contains(name) {
                    return Ok(device);
                }
            }
        }

        Err(anyhow!("Input device '{}' not found", name))
    }

    /// Find output device by name
    pub fn find_output_device(name: &str) -> Result<Device> {
        let host = cpal::default_host();

        for device in host.output_devices()? {
            if let Ok(device_name) = device.name() {
                if device_name.contains(name) {
                    return Ok(device);
                }
            }
        }

        Err(anyhow!("Output device '{}' not found", name))
    }

    /// Get default input device
    pub fn get_default_input_device() -> Result<Device> {
        let host = cpal::default_host();
        host.default_input_device()
            .ok_or_else(|| anyhow!("No default input device available"))
    }

    /// Get default output device
    pub fn get_default_output_device() -> Result<Device> {
        let host = cpal::default_host();
        host.default_output_device()
            .ok_or_else(|| anyhow!("No default output device available"))
    }

    /// Test audio input device
    pub fn test_input_device(device: &Device) -> Result<()> {
        let config = device.default_input_config()?;
        info!("Testing input device: {}", device.name().unwrap_or("Unknown".to_string()));
        info!("Default config: {:?}", config);

        // Try to build a test stream
        let stream = device.build_input_stream(
            &config.into(),
            |_data: &[f32], _: &cpal::InputCallbackInfo| {
                // Test callback - do nothing
            },
            |err| {
                error!("Audio input error: {}", err);
            },
            None,
        )?;

        drop(stream);
        info!("Input device test successful");
        Ok(())
    }

    /// Test audio output device
    pub fn test_output_device(device: &Device) -> Result<()> {
        let config = device.default_output_config()?;
        info!("Testing output device: {}", device.name().unwrap_or("Unknown".to_string()));
        info!("Default config: {:?}", config);

        // Try to build a test stream
        let stream = device.build_output_stream(
            &config.into(),
            |_data: &mut [f32], _: &cpal::OutputCallbackInfo| {
                // Test callback - output silence
                for sample in _data.iter_mut() {
                    *sample = 0.0;
                }
            },
            |err| {
                error!("Audio output error: {}", err);
            },
            None,
        )?;

        drop(stream);
        info!("Output device test successful");
        Ok(())
    }

    /// Calculate audio level (RMS) from samples
    pub fn calculate_audio_level(samples: &[f32]) -> f32 {
        if samples.is_empty() {
            return 0.0;
        }

        let sum_squares: f32 = samples.iter().map(|&x| x * x).sum();
        (sum_squares / samples.len() as f32).sqrt()
    }

    /// Apply simple noise gate to audio samples
    pub fn apply_noise_gate(samples: &mut [f32], threshold: f32) {
        for sample in samples.iter_mut() {
            if sample.abs() < threshold {
                *sample = 0.0;
            }
        }
    }

    /// Normalize audio samples to prevent clipping
    pub fn normalize_audio(samples: &mut [f32]) {
        let max_amplitude = samples.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);

        if max_amplitude > 1.0 {
            let scale_factor = 1.0 / max_amplitude;
            for sample in samples.iter_mut() {
                *sample *= scale_factor;
            }
        }
    }

    /// Convert stereo to mono by averaging channels
    pub fn stereo_to_mono(stereo_samples: &[f32]) -> Vec<f32> {
        let mut mono_samples = Vec::with_capacity(stereo_samples.len() / 2);

        for chunk in stereo_samples.chunks_exact(2) {
            let mono_sample = (chunk[0] + chunk[1]) / 2.0;
            mono_samples.push(mono_sample);
        }

        mono_samples
    }

    /// Convert mono to stereo by duplicating the channel
    pub fn mono_to_stereo(mono_samples: &[f32]) -> Vec<f32> {
        let mut stereo_samples = Vec::with_capacity(mono_samples.len() * 2);

        for &sample in mono_samples {
            stereo_samples.push(sample);
            stereo_samples.push(sample);
        }

        stereo_samples
    }

    /// Apply simple low-pass filter (smoothing)
    pub fn apply_low_pass_filter(samples: &mut [f32], alpha: f32) {
        if samples.is_empty() {
            return;
        }

        let alpha = alpha.clamp(0.0, 1.0);

        for i in 1..samples.len() {
            samples[i] = alpha * samples[i] + (1.0 - alpha) * samples[i - 1];
        }
    }

    /// Detect voice activity based on energy and zero-crossing rate
    pub fn detect_voice_activity(samples: &[f32], energy_threshold: f32, zcr_threshold: f32) -> bool {
        if samples.is_empty() {
            return false;
        }

        // Calculate energy (RMS)
        let energy = Self::calculate_audio_level(samples);

        // Calculate zero-crossing rate
        let mut zero_crossings = 0;
        for i in 1..samples.len() {
            if (samples[i] >= 0.0) != (samples[i - 1] >= 0.0) {
                zero_crossings += 1;
            }
        }
        let zcr = zero_crossings as f32 / samples.len() as f32;

        // Voice activity detected if energy is above threshold and ZCR is reasonable
        energy > energy_threshold && zcr < zcr_threshold
    }

    /// Log audio device information
    pub fn log_audio_devices() {
        info!("=== Audio Device Information ===");

        match Self::list_input_devices() {
            Ok(devices) => {
                info!("Input Devices:");
                for device in devices {
                    info!("  {} {}",
                        device.name,
                        if device.is_default { "(default)" } else { "" }
                    );
                }
            }
            Err(e) => error!("Failed to list input devices: {}", e),
        }

        match Self::list_output_devices() {
            Ok(devices) => {
                info!("Output Devices:");
                for device in devices {
                    info!("  {} {}",
                        device.name,
                        if device.is_default { "(default)" } else { "" }
                    );
                }
            }
            Err(e) => error!("Failed to list output devices: {}", e),
        }

        info!("===============================");
    }
}