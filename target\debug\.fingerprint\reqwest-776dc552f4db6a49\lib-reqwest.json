{"rustc": 3926191382657067107, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2241668132362809309, "path": 5223514829721474281, "deps": [[40386456601120721, "percent_encoding", false, 8128281750739369649], [95042085696191081, "ipnet", false, 7389273761950026503], [264090853244900308, "sync_wrapper", false, 8631227097907173709], [784494742817713399, "tower_service", false, 646946365883045892], [1906322745568073236, "pin_project_lite", false, 7519852132410875248], [3150220818285335163, "url", false, 10342121030197763969], [3722963349756955755, "once_cell", false, 12425926461976563839], [4405182208873388884, "http", false, 18180639592836484422], [5986029879202738730, "log", false, 11890952594027020941], [7414427314941361239, "hyper", false, 4130329339046416952], [7620660491849607393, "futures_core", false, 3988601973302101402], [8405603588346937335, "winreg", false, 13308743744770162947], [8915503303801890683, "http_body", false, 6007017654538460852], [9689903380558560274, "serde", false, 15801814292485297685], [10229185211513642314, "mime", false, 4995242822479558999], [10629569228670356391, "futures_util", false, 9623358684371792177], [12186126227181294540, "tokio_native_tls", false, 9781846887730240738], [12367227501898450486, "hyper_tls", false, 11303293513597550228], [12393800526703971956, "tokio", false, 17801960723477092550], [13809605890706463735, "h2", false, 7707333809229258695], [14564311161534545801, "encoding_rs", false, 17987725509700820899], [15367738274754116744, "serde_json", false, 9991360992155204765], [16066129441945555748, "bytes", false, 3986880018854615851], [16311359161338405624, "rustls_pemfile", false, 12536350635646129579], [16542808166767769916, "serde_urlencoded", false, 1774260650450901347], [16785601910559813697, "native_tls_crate", false, 12244438285670771550], [18066890886671768183, "base64", false, 9938452868969740752]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-776dc552f4db6a49\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}