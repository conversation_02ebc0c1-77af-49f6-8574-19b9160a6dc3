use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use dirs;

/// Main configuration structure for Matrix AI
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MatrixConfig {
    pub ai: AiConfig,
    pub voice: VoiceConfig,
    pub automation: AutomationConfig,
    pub system: SystemConfig,
    pub ui: UiConfig,
}

/// AI/LLM related configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AiConfig {
    pub ollama_url: String,
    pub model_name: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub timeout_seconds: u64,
}

/// Voice processing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoiceConfig {
    pub stt: SttConfig,
    pub tts: TtsConfig,
    pub audio: AudioConfig,
}

/// Speech-to-Text configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SttConfig {
    pub model_path: PathBuf,
    pub language: String,
    pub sample_rate: u32,
    pub chunk_duration_ms: u64,
}

/// Text-to-Speech configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TtsConfig {
    pub model_path: PathBuf,
    pub config_path: PathBuf,
    pub voice_speed: f32,
    pub voice_pitch: f32,
}

/// Audio system configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioConfig {
    pub input_device: Option<String>,
    pub output_device: Option<String>,
    pub sample_rate: u32,
    pub channels: u16,
    pub buffer_size: u32,
}

/// Automation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutomationConfig {
    pub whatsapp: WhatsAppConfig,
    pub browser: BrowserConfig,
    pub email: EmailConfig,
    pub system: SystemAutomationConfig,
}

/// WhatsApp automation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhatsAppConfig {
    pub python_script_path: PathBuf,
    pub chrome_driver_path: Option<PathBuf>,
    pub timeout_seconds: u64,
}

/// Browser automation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserConfig {
    pub default_browser: String,
    pub selenium_timeout: u64,
    pub headless: bool,
}

/// Email configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailConfig {
    pub smtp_server: String,
    pub smtp_port: u16,
    pub username: String,
    pub use_tls: bool,
}

/// System automation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAutomationConfig {
    pub mouse_speed: f32,
    pub keyboard_delay_ms: u64,
    pub screenshot_path: PathBuf,
}

/// System-wide configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    pub log_level: String,
    pub log_file: PathBuf,
    pub data_dir: PathBuf,
    pub models_dir: PathBuf,
    pub scripts_dir: PathBuf,
}

/// UI configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiConfig {
    pub enable_gui: bool,
    pub window_width: u32,
    pub window_height: u32,
    pub theme: String,
}

impl Default for MatrixConfig {
    fn default() -> Self {
        let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
        let matrix_dir = home_dir.join(".matrix-ai");

        Self {
            ai: AiConfig::default(),
            voice: VoiceConfig::default(&matrix_dir),
            automation: AutomationConfig::default(&matrix_dir),
            system: SystemConfig::default(&matrix_dir),
            ui: UiConfig::default(),
        }
    }
}

impl Default for AiConfig {
    fn default() -> Self {
        Self {
            ollama_url: "http://localhost:11434".to_string(),
            model_name: "tinyllama".to_string(),
            max_tokens: 512,
            temperature: 0.7,
            timeout_seconds: 30,
        }
    }
}

impl VoiceConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            stt: SttConfig::default(base_dir),
            tts: TtsConfig::default(base_dir),
            audio: AudioConfig::default(),
        }
    }
}

impl SttConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            model_path: base_dir.join("models/stt/ggml-base.en.bin"),
            language: "en".to_string(),
            sample_rate: 16000,
            chunk_duration_ms: 1000,
        }
    }
}

impl TtsConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            model_path: base_dir.join("models/tts/en_US-amy-low.onnx"),
            config_path: base_dir.join("models/tts/en_US-amy-low.onnx.json"),
            voice_speed: 1.0,
            voice_pitch: 1.0,
        }
    }
}

impl Default for AudioConfig {
    fn default() -> Self {
        Self {
            input_device: None,
            output_device: None,
            sample_rate: 16000,
            channels: 1,
            buffer_size: 1024,
        }
    }
}

impl AutomationConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            whatsapp: WhatsAppConfig::default(base_dir),
            browser: BrowserConfig::default(),
            email: EmailConfig::default(),
            system: SystemAutomationConfig::default(base_dir),
        }
    }
}

impl WhatsAppConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            python_script_path: base_dir.join("scripts/whatsapp.py"),
            chrome_driver_path: None,
            timeout_seconds: 30,
        }
    }
}

impl Default for BrowserConfig {
    fn default() -> Self {
        Self {
            default_browser: "chrome".to_string(),
            selenium_timeout: 30,
            headless: false,
        }
    }
}

impl Default for EmailConfig {
    fn default() -> Self {
        Self {
            smtp_server: "smtp.gmail.com".to_string(),
            smtp_port: 587,
            username: "".to_string(),
            use_tls: true,
        }
    }
}

impl SystemAutomationConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            mouse_speed: 1.0,
            keyboard_delay_ms: 50,
            screenshot_path: base_dir.join("screenshots"),
        }
    }
}

impl SystemConfig {
    fn default(base_dir: &PathBuf) -> Self {
        Self {
            log_level: "info".to_string(),
            log_file: base_dir.join("logs/matrix-ai.log"),
            data_dir: base_dir.clone(),
            models_dir: base_dir.join("models"),
            scripts_dir: base_dir.join("scripts"),
        }
    }
}

impl Default for UiConfig {
    fn default() -> Self {
        Self {
            enable_gui: true,
            window_width: 800,
            window_height: 600,
            theme: "dark".to_string(),
        }
    }
}

/// Load configuration from file or create default
pub fn load_config() -> anyhow::Result<MatrixConfig> {
    let config_path = get_config_path();

    if config_path.exists() {
        let config_str = std::fs::read_to_string(&config_path)?;
        let config: MatrixConfig = serde_json::from_str(&config_str)?;
        Ok(config)
    } else {
        let config = MatrixConfig::default();
        save_config(&config)?;
        Ok(config)
    }
}

/// Save configuration to file
pub fn save_config(config: &MatrixConfig) -> anyhow::Result<()> {
    let config_path = get_config_path();

    if let Some(parent) = config_path.parent() {
        std::fs::create_dir_all(parent)?;
    }

    let config_str = serde_json::to_string_pretty(config)?;
    std::fs::write(&config_path, config_str)?;

    Ok(())
}

/// Get the configuration file path
fn get_config_path() -> PathBuf {
    let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
    home_dir.join(".matrix-ai/config.json")
}

/// Ensure all required directories exist
pub fn ensure_directories(config: &MatrixConfig) -> anyhow::Result<()> {
    let dirs_to_create = vec![
        &config.system.data_dir,
        &config.system.models_dir,
        &config.system.scripts_dir,
        config.system.log_file.parent().unwrap(),
        &config.automation.system.screenshot_path,
    ];

    for dir in dirs_to_create {
        if !dir.exists() {
            std::fs::create_dir_all(dir)?;
        }
    }

    Ok(())
}