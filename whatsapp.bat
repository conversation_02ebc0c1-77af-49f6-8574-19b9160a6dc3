@echo off
:: Matrix AI WhatsApp Automation Launcher

echo.
echo 📱 Matrix AI - WhatsApp Automation
echo ═══════════════════════════════════
echo.

cd /d "%~dp0"

if "%1"=="" (
    echo Usage: whatsapp.bat "Contact Name" "Message"
    echo Example: whatsapp.bat "<PERSON>" "Hello from Matrix AI!"
    echo.
    echo For more options:
    python scripts\whatsapp.py --help
    pause
    exit /b 1
)

echo 🚀 Sending WhatsApp message...
echo Contact: %1
echo Message: %2
echo.

python scripts\whatsapp.py --contact %1 --message %2 %3 %4 %5 %6 %7 %8 %9

if %errorlevel% equ 0 (
    echo ✅ Message sent successfully!
) else (
    echo ❌ Failed to send message
)

pause
