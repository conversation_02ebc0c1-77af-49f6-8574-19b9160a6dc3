[package]
name = "matrix-ai"
version = "0.1.0"
edition = "2021"
description = "Matrix AI - Autonomous Offline AI Assistant"
authors = ["Matrix AI Team"]
license = "MIT"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# HTTP client for Ollama communication
reqwest = { version = "0.11", features = ["json"] }

# Audio input/output
cpal = "0.15"
rodio = "0.17"
hound = "3.5"

# System automation
enigo = "0.2"
open = "5.0"

# Process execution
duct = "0.13"

# Logging
log = "0.4"
env_logger = "0.10"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# File system operations
walkdir = "2.0"

# Time utilities
chrono = { version = "0.4", features = ["serde"] }

# Configuration
config = "0.14"

# Cross-platform paths
dirs = "5.0"

# Regex for parsing
regex = "1.0"

# UUID generation
uuid = { version = "1.0", features = ["v4"] }

# Tauri for UI (optional)
tauri = { version = "1.0", features = ["api-all"], optional = true }

# Additional audio processing
symphonia = { version = "0.5", features = ["all"] }

# System information
sysinfo = "0.29"

# Command line argument parsing
clap = { version = "4.0", features = ["derive"] }

# Futures utilities
futures = "0.3"

# Crossbeam for concurrent programming
crossbeam = "0.8"

# Parking lot for better mutexes
parking_lot = "0.12"

# Base64 encoding/decoding
base64 = "0.21"

# URL parsing
url = "2.0"

[features]
default = []
ui = ["tauri"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true

[[bin]]
name = "matrix-ai"
path = "src/main.rs"
