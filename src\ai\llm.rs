use crate::prelude::*;
use crate::config::AiConfig;
use reqwest::Client;
use serde_json::{json, Value};
use std::time::Duration;

/// Client for communicating with Ollama LLM
pub struct LlmClient {
    client: Client,
    config: AiConfig,
}

#[derive(Debug, Serialize, Deserialize)]
struct OllamaRequest {
    model: String,
    prompt: String,
    stream: bool,
    options: OllamaOptions,
}

#[derive(Debug, Serialize, Deserialize)]
struct OllamaOptions {
    temperature: f32,
    num_predict: u32,
}

#[derive(Debug, Serialize, Deserialize)]
struct OllamaResponse {
    response: String,
    done: bool,
}

impl LlmClient {
    /// Create a new LLM client
    pub async fn new(config: &AiConfig) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .context("Failed to create HTTP client")?;

        let llm_client = Self {
            client,
            config: config.clone(),
        };

        // Test connection to Ollama
        llm_client.test_connection().await?;

        Ok(llm_client)
    }

    /// Test connection to Ollama server
    async fn test_connection(&self) -> Result<()> {
        let url = format!("{}/api/tags", self.config.ollama_url);

        let response = self.client
            .get(&url)
            .send()
            .await
            .context("Failed to connect to Ollama server")?;

        if !response.status().is_success() {
            return Err(anyhow!("Ollama server returned error: {}", response.status()));
        }

        info!("Successfully connected to Ollama server at {}", self.config.ollama_url);
        Ok(())
    }

    /// Send a query to the LLM and get response
    pub async fn query(&self, prompt: &str) -> Result<String> {
        let enhanced_prompt = self.enhance_prompt(prompt);

        let request = OllamaRequest {
            model: self.config.model_name.clone(),
            prompt: enhanced_prompt,
            stream: false,
            options: OllamaOptions {
                temperature: self.config.temperature,
                num_predict: self.config.max_tokens,
            },
        };

        let url = format!("{}/api/generate", self.config.ollama_url);

        debug!("Sending request to LLM: {}", prompt);

        let response = self.client
            .post(&url)
            .json(&request)
            .send()
            .await
            .context("Failed to send request to Ollama")?;

        if !response.status().is_success() {
            return Err(anyhow!("LLM request failed with status: {}", response.status()));
        }

        let response_text = response.text().await
            .context("Failed to read response from Ollama")?;

        let ollama_response: OllamaResponse = serde_json::from_str(&response_text)
            .context("Failed to parse Ollama response")?;

        debug!("LLM response: {}", ollama_response.response);

        Ok(ollama_response.response.trim().to_string())
    }

    /// Enhance the user prompt with system instructions
    fn enhance_prompt(&self, user_prompt: &str) -> String {
        format!(
            r#"You are Matrix AI, an autonomous offline AI assistant. Your role is to understand user commands and provide clear, actionable responses.

When the user gives you a command, respond with a structured format that includes:
1. INTENT: The main action they want (e.g., "open_app", "send_message", "search_web", "system_control")
2. TARGET: What they want to act on (e.g., app name, contact name, search query)
3. DETAILS: Any additional parameters or context
4. RESPONSE: A natural language response to the user

Examples:
User: "Open Chrome"
INTENT: open_app
TARGET: chrome
DETAILS: browser
RESPONSE: Opening Chrome browser for you.

User: "Send a message to John saying hello"
INTENT: send_message
TARGET: John
DETAILS: hello
RESPONSE: Sending message to John.

User: "What's the weather like?"
INTENT: search_web
TARGET: weather
DETAILS: current weather
RESPONSE: Let me check the current weather for you.

Now respond to this user command:
User: {}"#,
            user_prompt
        )
    }

    /// Check if the LLM model is available
    pub async fn check_model_availability(&self) -> Result<bool> {
        let url = format!("{}/api/tags", self.config.ollama_url);

        let response = self.client
            .get(&url)
            .send()
            .await
            .context("Failed to check model availability")?;

        let response_json: Value = response.json().await
            .context("Failed to parse model list response")?;

        if let Some(models) = response_json["models"].as_array() {
            for model in models {
                if let Some(name) = model["name"].as_str() {
                    if name.contains(&self.config.model_name) {
                        return Ok(true);
                    }
                }
            }
        }

        Ok(false)
    }

    /// Pull the model if it's not available
    pub async fn ensure_model(&self) -> Result<()> {
        if !self.check_model_availability().await? {
            info!("Model {} not found, attempting to pull...", self.config.model_name);
            self.pull_model().await?;
        }
        Ok(())
    }

    /// Pull the specified model from Ollama
    async fn pull_model(&self) -> Result<()> {
        let url = format!("{}/api/pull", self.config.ollama_url);

        let request = json!({
            "name": self.config.model_name
        });

        let response = self.client
            .post(&url)
            .json(&request)
            .send()
            .await
            .context("Failed to pull model")?;

        if !response.status().is_success() {
            return Err(anyhow!("Failed to pull model: {}", response.status()));
        }

        info!("Successfully pulled model: {}", self.config.model_name);
        Ok(())
    }
}