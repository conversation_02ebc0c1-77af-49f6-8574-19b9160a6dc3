use matrix_ai::prelude::*;
use matrix_ai::{
    ai::{llm::LlmClient, parser::IntentParser},
    voice::{stt::SttEngine, tts::TtsEngine},
    automation::AutomationEngine,
    utils::logging::init_logging,
};
use clap::{Arg, Command};
use std::sync::Arc;
use tokio::sync::Mutex;

/// Main Matrix AI application state
pub struct MatrixAI {
    config: MatrixConfig,
    llm_client: Arc<LlmClient>,
    intent_parser: Arc<IntentParser>,
    stt_engine: Arc<Mutex<SttEngine>>,
    tts_engine: Arc<Mutex<TtsEngine>>,
    automation_engine: Arc<AutomationEngine>,
}

impl MatrixAI {
    /// Initialize Matrix AI with configuration
    pub async fn new(config: MatrixConfig) -> Result<Self> {
        info!("Initializing Matrix AI...");

        // Initialize components
        let llm_client = Arc::new(LlmClient::new(&config.ai).await?);
        let intent_parser = Arc::new(IntentParser::new());
        let stt_engine = Arc::new(Mutex::new(SttEngine::new(&config.voice.stt).await?));
        let tts_engine = Arc::new(Mutex::new(TtsEngine::new(&config.voice.tts).await?));
        let automation_engine = Arc::new(AutomationEngine::new(&config.automation).await?);

        info!("Matrix AI initialized successfully");

        Ok(Self {
            config,
            llm_client,
            intent_parser,
            stt_engine,
            tts_engine,
            automation_engine,
        })
    }

    /// Main control loop: STT → LLM → Parser → Automation → TTS
    pub async fn run(&self) -> Result<()> {
        info!("Starting Matrix AI main control loop...");

        loop {
            match self.process_voice_input().await {
                Ok(_) => {
                    debug!("Voice input processed successfully");
                }
                Err(e) => {
                    error!("Error processing voice input: {}", e);
                    // Continue running even if there's an error
                }
            }

            // Small delay to prevent excessive CPU usage
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }
    }

    /// Process a single voice input cycle
    async fn process_voice_input(&self) -> Result<()> {
        // Step 1: Speech-to-Text
        let user_text = {
            let mut stt = self.stt_engine.lock().await;
            stt.listen_for_speech().await?
        };

        if user_text.trim().is_empty() {
            return Ok(()); // No speech detected
        }

        info!("User said: {}", user_text);

        // Step 2: Send to LLM for processing
        let llm_response = self.llm_client.query(&user_text).await?;
        debug!("LLM response: {}", llm_response);

        // Step 3: Parse intent from LLM response
        let intent = self.intent_parser.parse(&llm_response)?;
        debug!("Parsed intent: {:?}", intent);

        // Step 4: Execute automation based on intent
        let automation_result = self.automation_engine.execute(&intent).await?;

        // Step 5: Generate response text
        let response_text = self.generate_response(&intent, &automation_result).await?;
        info!("Response: {}", response_text);

        // Step 6: Text-to-Speech
        {
            let mut tts = self.tts_engine.lock().await;
            tts.speak(&response_text).await?;
        }

        Ok(())
    }

    /// Generate appropriate response based on intent and automation result
    async fn generate_response(&self, intent: &Intent, result: &AutomationResult) -> Result<String> {
        let response = match result {
            AutomationResult::Success(message) => {
                format!("Done. {}", message)
            }
            AutomationResult::Error(error) => {
                format!("Sorry, I couldn't complete that task. {}", error)
            }
            AutomationResult::Info(info) => {
                info.clone()
            }
        };

        Ok(response)
    }

    /// Shutdown Matrix AI gracefully
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down Matrix AI...");

        // Cleanup resources if needed
        {
            let mut stt = self.stt_engine.lock().await;
            stt.shutdown().await?;
        }

        {
            let mut tts = self.tts_engine.lock().await;
            tts.shutdown().await?;
        }

        info!("Matrix AI shutdown complete");
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let matches = Command::new("Matrix AI")
        .version("0.1.0")
        .author("Matrix AI Team")
        .about("Autonomous Offline AI Assistant")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("Sets a custom config file")
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .action(clap::ArgAction::Count)
                .help("Sets the level of verbosity")
        )
        .get_matches();

    // Load configuration
    let config = load_config().context("Failed to load configuration")?;

    // Ensure required directories exist
    ensure_directories(&config).context("Failed to create required directories")?;

    // Initialize logging
    init_logging(&config.system.log_level, &config.system.log_file)?;

    info!("Matrix AI starting up...");
    info!("Configuration loaded from: {:?}", config.system.data_dir);

    // Initialize Matrix AI
    let matrix_ai = MatrixAI::new(config).await.context("Failed to initialize Matrix AI")?;

    // Set up graceful shutdown
    let matrix_ai = Arc::new(matrix_ai);
    let matrix_ai_clone = matrix_ai.clone();

    // Handle Ctrl+C for graceful shutdown
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl-c");
        info!("Received shutdown signal");
        if let Err(e) = matrix_ai_clone.shutdown().await {
            error!("Error during shutdown: {}", e);
        }
        std::process::exit(0);
    });

    // Run the main loop
    matrix_ai.run().await.context("Matrix AI main loop failed")?;

    Ok(())
}

// Import the types we'll need from other modules
use matrix_ai::automation::{Intent, AutomationResult};