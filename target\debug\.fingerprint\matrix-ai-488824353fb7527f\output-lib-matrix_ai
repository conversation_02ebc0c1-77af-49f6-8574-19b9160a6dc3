{"$message_type":"diagnostic","message":"environment variable `RUSTC_VERSION` not defined at compile time","code":null,"level":"error","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":2212,"byte_end":2233,"line_start":72,"line_end":72,"column_start":31,"column_end":52,"is_primary":true,"text":[{"text":"    info!(\"Rust version: {}\", env!(\"RUSTC_VERSION\"));","highlight_start":31,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\utils\\logging.rs","byte_start":2212,"byte_end":2233,"line_start":72,"line_end":72,"column_start":31,"column_end":52,"is_primary":false,"text":[{"text":"    info!(\"Rust version: {}\", env!(\"RUSTC_VERSION\"));","highlight_start":31,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"env!","def_site_span":{"file_name":"/rustc/6b00bc3880198600130e1cf62b8f8a93494488cc\\library\\core\\src\\macros\\mod.rs","byte_start":37567,"byte_end":37583,"line_start":1075,"line_end":1075,"column_start":5,"column_end":21,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"use `std::env::var(\"RUSTC_VERSION\")` to read the variable at run time","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: environment variable `RUSTC_VERSION` not defined at compile time\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\logging.rs:72:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    info!(\"Rust version: {}\", env!(\"RUSTC_VERSION\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: use `std::env::var(\"RUSTC_VERSION\")` to read the variable at run time\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the name `Intent` is defined multiple times","code":{"code":"E0252","explanation":"Two items of the same name cannot be imported without rebinding one of the\nitems under a new local name.\n\nErroneous code example:\n\n```compile_fail,E0252\nuse foo::baz;\nuse bar::baz; // error, do `use bar::baz as quux` instead\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse foo::baz as foo_baz;\nuse bar::baz; // ok!\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nOr you can reference the item with its parent:\n\n```\nuse bar::baz;\n\nfn main() {\n    let x = foo::baz; // ok!\n}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\automation\\mod.rs","byte_start":6211,"byte_end":6217,"line_start":173,"line_end":173,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"pub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};","highlight_start":29,"highlight_end":35}],"label":"`Intent` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\automation\\mod.rs","byte_start":153,"byte_end":178,"line_start":9,"line_end":9,"column_start":5,"column_end":30,"is_primary":false,"text":[{"text":"use crate::ai::parser::Intent;","highlight_start":5,"highlight_end":30}],"label":"previous import of the type `Intent` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Intent` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"src\\automation\\mod.rs","byte_start":6211,"byte_end":6219,"line_start":173,"line_end":173,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"pub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0252]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `Intent` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\mod.rs:173:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ai::parser::Intent;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the type `Intent` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`Intent` reimported here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove unnecessary import\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Intent` must be defined only once in the type namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\ai\\parser.rs","byte_start":47,"byte_end":72,"line_start":3,"line_end":3,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\ai\\parser.rs","byte_start":43,"byte_end":75,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ai\\parser.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Stream` and `SupportedStreamConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":80,"byte_end":86,"line_start":3,"line_end":3,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\voice\\stt.rs","byte_start":102,"byte_end":123,"line_start":3,"line_end":3,"column_start":48,"column_end":69,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};","highlight_start":48,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":78,"byte_end":86,"line_start":3,"line_end":3,"column_start":24,"column_end":32,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};","highlight_start":24,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\voice\\stt.rs","byte_start":100,"byte_end":123,"line_start":3,"line_end":3,"column_start":46,"column_end":69,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};","highlight_start":46,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Stream` and `SupportedStreamConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\voice\\stt.rs:3:26\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cpal::{Device, Host, Stream, StreamConfig, SupportedStreamConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fs::File`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":239,"byte_end":252,"line_start":7,"line_end":7,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":235,"byte_end":255,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":1,"highlight_end":19},{"text":"use std::io::Write;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fs::File`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\voice\\stt.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fs::File;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::io::Write`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":259,"byte_end":273,"line_start":8,"line_end":8,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::Write;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":255,"byte_end":276,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::io::Write;","highlight_start":1,"highlight_end":20},{"text":"use hound::{WavWriter, WavSpec};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::io::Write`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\voice\\stt.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::Write;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Intent`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\automation\\mod.rs","byte_start":6211,"byte_end":6217,"line_start":173,"line_end":173,"column_start":29,"column_end":35,"is_primary":true,"text":[{"text":"pub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};","highlight_start":29,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\automation\\mod.rs","byte_start":6211,"byte_end":6219,"line_start":173,"line_end":173,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"pub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Intent`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\mod.rs:173:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use crate::ai::parser::{Intent, MessagePlatform, SystemAction, BrowserAction, EmailActionType};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Host` and `SupportedStreamConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\audio.rs","byte_start":43,"byte_end":47,"line_start":2,"line_end":2,"column_start":20,"column_end":24,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, SupportedStreamConfig};","highlight_start":20,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils\\audio.rs","byte_start":49,"byte_end":70,"line_start":2,"line_end":2,"column_start":26,"column_end":47,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, SupportedStreamConfig};","highlight_start":26,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\utils\\audio.rs","byte_start":41,"byte_end":70,"line_start":2,"line_end":2,"column_start":18,"column_end":47,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, SupportedStreamConfig};","highlight_start":18,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\audio.rs","byte_start":34,"byte_end":35,"line_start":2,"line_end":2,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, SupportedStreamConfig};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\utils\\audio.rs","byte_start":70,"byte_end":71,"line_start":2,"line_end":2,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"use cpal::{Device, Host, SupportedStreamConfig};","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Host` and `SupportedStreamConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\audio.rs:2:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cpal::{Device, Host, SupportedStreamConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `supported_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":1046,"byte_end":1062,"line_start":34,"line_end":34,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let supported_config = input_device","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\voice\\stt.rs","byte_start":1046,"byte_end":1062,"line_start":34,"line_end":34,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let supported_config = input_device","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":"_supported_config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `supported_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\voice\\stt.rs:34:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let supported_config = input_device\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_supported_config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `subject`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\automation\\email.rs","byte_start":438,"byte_end":445,"line_start":16,"line_end":16,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {","highlight_start":53,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\automation\\email.rs","byte_start":438,"byte_end":445,"line_start":16,"line_end":16,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {","highlight_start":53,"highlight_end":60}],"label":null,"suggested_replacement":"_subject","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `subject`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\email.rs:16:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_subject`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `body`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\automation\\email.rs","byte_start":453,"byte_end":457,"line_start":16,"line_end":16,"column_start":68,"column_end":72,"is_primary":true,"text":[{"text":"    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {","highlight_start":68,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\automation\\email.rs","byte_start":453,"byte_end":457,"line_start":16,"line_end":16,"column_start":68,"column_end":72,"is_primary":true,"text":[{"text":"    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {","highlight_start":68,"highlight_end":72}],"label":null,"suggested_replacement":"_body","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `body`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\email.rs:16:68\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_email(&self, recipient: &str, subject: &str, body: &str) -> Result<AutomationResult> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_body`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `message`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\automation\\whatsapp.rs","byte_start":439,"byte_end":446,"line_start":16,"line_end":16,"column_start":52,"column_end":59,"is_primary":true,"text":[{"text":"    pub async fn send_message(&self, target: &str, message: &str) -> Result<AutomationResult> {","highlight_start":52,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\automation\\whatsapp.rs","byte_start":439,"byte_end":446,"line_start":16,"line_end":16,"column_start":52,"column_end":59,"is_primary":true,"text":[{"text":"    pub async fn send_message(&self, target: &str, message: &str) -> Result<AutomationResult> {","highlight_start":52,"highlight_end":59}],"label":null,"suggested_replacement":"_message","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `message`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\whatsapp.rs:16:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn send_message(&self, target: &str, message: &str) -> Result<AutomationResult> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_message`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `processors` found for struct `sysinfo::System` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":3093,"byte_end":3103,"line_start":102,"line_end":102,"column_start":24,"column_end":34,"is_primary":true,"text":[{"text":"        cpu_cores: sys.processors().len(),","highlight_start":24,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `process` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sysinfo-0.29.11\\src\\traits.rs","byte_start":31857,"byte_end":31905,"line_start":993,"line_end":993,"column_start":5,"column_end":53,"is_primary":true,"text":[{"text":"    fn process(&self, pid: Pid) -> Option<&Process>;","highlight_start":5,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `processors` found for struct `sysinfo::System` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\logging.rs:102:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        cpu_cores: sys.processors().len(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `process` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sysinfo-0.29.11\\src\\traits.rs:993:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m993\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn process(&self, pid: Pid) -> Option<&Process>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `hour` found for struct `DateTime` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\chrono-0.4.41\\src\\traits.rs","byte_start":11059,"byte_end":11063,"line_start":285,"line_end":285,"column_start":8,"column_end":12,"is_primary":false,"text":[{"text":"    fn hour(&self) -> u32;","highlight_start":8,"highlight_end":12}],"label":"the method is available for `DateTime<Local>` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils\\time.rs","byte_start":3932,"byte_end":3936,"line_start":119,"line_end":119,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        let hour = now.hour();","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"trait `Timelike` which provides `hour` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\utils\\time.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::prelude::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use chrono::Timelike;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"there is a method `hour12` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\utils\\time.rs","byte_start":3932,"byte_end":3936,"line_start":119,"line_end":119,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        let hour = now.hour();","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":"hour12","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `hour` found for struct `DateTime` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:119:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let hour = now.hour();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\chrono-0.4.41\\src\\traits.rs:285:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m285\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn hour(&self) -> u32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthe method is available for `DateTime<Local>` here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Timelike` which provides `hour` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use chrono::Timelike;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `hour12` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let hour = now.hour\u001b[0m\u001b[0m\u001b[38;5;10m12\u001b[0m\u001b[0m();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `hour` found for struct `DateTime` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\chrono-0.4.41\\src\\traits.rs","byte_start":11059,"byte_end":11063,"line_start":285,"line_end":285,"column_start":8,"column_end":12,"is_primary":false,"text":[{"text":"    fn hour(&self) -> u32;","highlight_start":8,"highlight_end":12}],"label":"the method is available for `DateTime<Local>` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\utils\\time.rs","byte_start":4144,"byte_end":4148,"line_start":126,"line_end":126,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        let hour = now.hour();","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"trait `Timelike` which provides `hour` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\utils\\time.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::prelude::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use chrono::Timelike;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"there is a method `hour12` with a similar name","code":null,"level":"help","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4144,"byte_end":4148,"line_start":126,"line_end":126,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"        let hour = now.hour();","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":"hour12","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `hour` found for struct `DateTime` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:126:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let hour = now.hour();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\chrono-0.4.41\\src\\traits.rs:285:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m285\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn hour(&self) -> u32;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthe method is available for `DateTime<Local>` here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Timelike` which provides `hour` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use chrono::Timelike;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `hour12` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let hour = now.hour\u001b[0m\u001b[0m\u001b[38;5;10m12\u001b[0m\u001b[0m();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::DateTime::<Tz>::date`: Use `date_naive()` instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4359,"byte_end":4363,"line_start":134,"line_end":134,"column_start":30,"column_end":34,"is_primary":true,"text":[{"text":"            let target = now.date().and_hms(9, 0, 0);","highlight_start":30,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::DateTime::<Tz>::date`: Use `date_naive()` instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:134:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let target = now.date().and_hms(9, 0, 0);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::Date::<Tz>::and_hms`: Use and_hms_opt() instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4366,"byte_end":4373,"line_start":134,"line_end":134,"column_start":37,"column_end":44,"is_primary":true,"text":[{"text":"            let target = now.date().and_hms(9, 0, 0);","highlight_start":37,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::Date::<Tz>::and_hms`: Use and_hms_opt() instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:134:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let target = now.date().and_hms(9, 0, 0);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::DateTime::<Tz>::date`: Use `date_naive()` instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4522,"byte_end":4526,"line_start":138,"line_end":138,"column_start":32,"column_end":36,"is_primary":true,"text":[{"text":"            let tomorrow = now.date().succ();","highlight_start":32,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::DateTime::<Tz>::date`: Use `date_naive()` instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:138:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let tomorrow = now.date().succ();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::Date::<Tz>::succ`: Use succ_opt() instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4529,"byte_end":4533,"line_start":138,"line_end":138,"column_start":39,"column_end":43,"is_primary":true,"text":[{"text":"            let tomorrow = now.date().succ();","highlight_start":39,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::Date::<Tz>::succ`: Use succ_opt() instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:138:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let tomorrow = now.date().succ();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `chrono::Date::<Tz>::and_hms`: Use and_hms_opt() instead","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\time.rs","byte_start":4572,"byte_end":4579,"line_start":139,"line_end":139,"column_start":35,"column_end":42,"is_primary":true,"text":[{"text":"            let target = tomorrow.and_hms(9, 0, 0);","highlight_start":35,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated method `chrono::Date::<Tz>::and_hms`: Use and_hms_opt() instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\time.rs:139:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let target = tomorrow.and_hms(9, 0, 0);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `command`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\automation\\apps.rs","byte_start":6169,"byte_end":6176,"line_start":153,"line_end":153,"column_start":55,"column_end":62,"is_primary":false,"text":[{"text":"        self.app_mappings.insert(name.to_lowercase(), command);","highlight_start":55,"highlight_end":62}],"label":"value moved here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\automation\\apps.rs","byte_start":6232,"byte_end":6239,"line_start":154,"line_end":154,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"        debug!(\"Added app mapping: {} -> {}\", name, command);","highlight_start":53,"highlight_end":60}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":4571,"byte_end":4616,"line_start":140,"line_end":140,"column_start":17,"column_end":62,"is_primary":false,"text":[{"text":"                $crate::__private_api::format_args!($($arg)+),","highlight_start":17,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":3239,"byte_end":3426,"line_start":108,"line_end":113,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::__log!(","highlight_start":9,"highlight_end":24},{"text":"            logger: $crate::__log_logger!(__log_global_logger),","highlight_start":1,"highlight_end":64},{"text":"            target: $crate::__private_api::module_path!(),","highlight_start":1,"highlight_end":59},{"text":"            $lvl,","highlight_start":1,"highlight_end":18},{"text":"            $($arg)+","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":10889,"byte_end":10933,"line_start":312,"line_end":312,"column_start":23,"column_end":67,"is_primary":false,"text":[{"text":"    ($($arg:tt)+) => ($crate::log!($crate::Level::Debug, $($arg)+))","highlight_start":23,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\automation\\apps.rs","byte_start":6188,"byte_end":6240,"line_start":154,"line_end":154,"column_start":9,"column_end":61,"is_primary":false,"text":[{"text":"        debug!(\"Added app mapping: {} -> {}\", name, command);","highlight_start":9,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"debug!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":9909,"byte_end":9927,"line_start":292,"line_end":292,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! debug {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::log!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":2206,"byte_end":2222,"line_start":75,"line_end":75,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"macro_rules! log {","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__log!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\log-0.4.27\\src\\macros.rs","byte_start":3469,"byte_end":3487,"line_start":119,"line_end":119,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! __log {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__private_api::format_args!","def_site_span":{"file_name":"/rustc/6b00bc3880198600130e1cf62b8f8a93494488cc\\library\\core\\src\\macros\\mod.rs","byte_start":34889,"byte_end":34913,"line_start":1004,"line_end":1004,"column_start":5,"column_end":29,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\automation\\apps.rs","byte_start":6095,"byte_end":6102,"line_start":152,"line_end":152,"column_start":53,"column_end":60,"is_primary":false,"text":[{"text":"    pub fn add_app_mapping(&mut self, name: String, command: String) {","highlight_start":53,"highlight_end":60}],"label":"move occurs because `command` has type `std::string::String`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider cloning the value if the performance cost is acceptable","code":null,"level":"help","spans":[{"file_name":"src\\automation\\apps.rs","byte_start":6176,"byte_end":6176,"line_start":153,"line_end":153,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"        self.app_mappings.insert(name.to_lowercase(), command);","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":".clone()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: borrow of moved value: `command`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\automation\\apps.rs:154:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_app_mapping(&mut self, name: String, command: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove occurs because `command` has type `std::string::String`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.app_mappings.insert(name.to_lowercase(), command);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvalue moved here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        debug!(\"Added app mapping: {} -> {}\", name, command);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::__private_api::format_args` which comes from the expansion of the macro `debug` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider cloning the value if the performance cost is acceptable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        self.app_mappings.insert(name.to_lowercase(), command\u001b[0m\u001b[0m\u001b[38;5;10m.clone()\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `file`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":1630,"byte_end":1634,"line_start":54,"line_end":54,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let file = OpenOptions::new()","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":1630,"byte_end":1634,"line_start":54,"line_end":54,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let file = OpenOptions::new()","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_file","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `file`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\logging.rs:54:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let file = OpenOptions::new()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_file`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `level`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":1585,"byte_end":1590,"line_start":53,"line_end":53,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"fn setup_file_logging(log_file: &Path, level: LevelFilter) -> Result<()> {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\utils\\logging.rs","byte_start":1585,"byte_end":1590,"line_start":53,"line_end":53,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"fn setup_file_logging(log_file: &Path, level: LevelFilter) -> Result<()> {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":"_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `level`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\utils\\logging.rs:53:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn setup_file_logging(log_file: &Path, level: LevelFilter) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_level`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 6 previous errors; 17 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 6 previous errors; 17 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0252, E0382, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0252, E0382, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0252`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0252`.\u001b[0m\n"}
