use crate::prelude::*;
use chrono::{DateTime, Utc, Local, Duration};
use std::time::{Instant, SystemTime, UNIX_EPOCH};

/// Time utilities for Matrix AI
pub struct TimeUtils;

impl TimeUtils {
    /// Get current UTC timestamp
    pub fn now_utc() -> DateTime<Utc> {
        Utc::now()
    }

    /// Get current local timestamp
    pub fn now_local() -> DateTime<Local> {
        Local::now()
    }

    /// Get current Unix timestamp in seconds
    pub fn unix_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }

    /// Get current Unix timestamp in milliseconds
    pub fn unix_timestamp_millis() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }

    /// Format timestamp for logging
    pub fn format_for_log(timestamp: DateTime<Utc>) -> String {
        timestamp.format("%Y-%m-%d %H:%M:%S%.3f UTC").to_string()
    }

    /// Format timestamp for display
    pub fn format_for_display(timestamp: DateTime<Local>) -> String {
        timestamp.format("%Y-%m-%d %H:%M:%S").to_string()
    }

    /// Parse ISO 8601 timestamp
    pub fn parse_iso8601(timestamp_str: &str) -> Result<DateTime<Utc>> {
        DateTime::parse_from_rfc3339(timestamp_str)
            .map(|dt| dt.with_timezone(&Utc))
            .context("Failed to parse ISO 8601 timestamp")
    }

    /// Get time elapsed since a given instant
    pub fn elapsed_since(start: Instant) -> std::time::Duration {
        start.elapsed()
    }

    /// Get time elapsed in human-readable format
    pub fn elapsed_human_readable(start: Instant) -> String {
        let elapsed = start.elapsed();
        Self::duration_human_readable(elapsed)
    }

    /// Convert duration to human-readable format
    pub fn duration_human_readable(duration: std::time::Duration) -> String {
        let total_seconds = duration.as_secs();
        let milliseconds = duration.subsec_millis();

        if total_seconds >= 3600 {
            let hours = total_seconds / 3600;
            let minutes = (total_seconds % 3600) / 60;
            let seconds = total_seconds % 60;
            format!("{}h {}m {}s", hours, minutes, seconds)
        } else if total_seconds >= 60 {
            let minutes = total_seconds / 60;
            let seconds = total_seconds % 60;
            format!("{}m {}s", minutes, seconds)
        } else if total_seconds > 0 {
            format!("{}.{:03}s", total_seconds, milliseconds)
        } else {
            format!("{}ms", milliseconds)
        }
    }

    /// Sleep for a specified duration
    pub async fn sleep(duration: std::time::Duration) {
        tokio::time::sleep(duration).await;
    }

    /// Sleep for milliseconds
    pub async fn sleep_ms(milliseconds: u64) {
        tokio::time::sleep(std::time::Duration::from_millis(milliseconds)).await;
    }

    /// Sleep for seconds
    pub async fn sleep_secs(seconds: u64) {
        tokio::time::sleep(std::time::Duration::from_secs(seconds)).await;
    }

    /// Create a timeout for an operation
    pub async fn timeout<T>(
        duration: std::time::Duration,
        future: impl std::future::Future<Output = T>,
    ) -> Result<T> {
        tokio::time::timeout(duration, future)
            .await
            .context("Operation timed out")
    }

    /// Get system uptime (if available)
    pub fn system_uptime() -> Option<std::time::Duration> {
        // This is a simplified implementation
        // In a real system, you might want to use platform-specific APIs
        None
    }

    /// Check if it's within business hours (9 AM - 5 PM local time)
    pub fn is_business_hours() -> bool {
        let now = Local::now();
        let hour = now.hour();
        hour >= 9 && hour < 17
    }

    /// Get time until next business hours
    pub fn time_until_business_hours() -> Option<Duration> {
        let now = Local::now();
        let hour = now.hour();

        if Self::is_business_hours() {
            return None; // Already in business hours
        }

        if hour < 9 {
            // Before business hours today
            let target = now.date().and_hms(9, 0, 0);
            Some(target - now)
        } else {
            // After business hours, next business day
            let tomorrow = now.date().succ();
            let target = tomorrow.and_hms(9, 0, 0);
            Some(target - now)
        }
    }

    /// Create a recurring timer
    pub fn create_interval(duration: std::time::Duration) -> tokio::time::Interval {
        tokio::time::interval(duration)
    }

    /// Measure execution time of a function
    pub async fn measure_async<T, F>(operation: F) -> (T, std::time::Duration)
    where
        F: std::future::Future<Output = T>,
    {
        let start = Instant::now();
        let result = operation.await;
        let duration = start.elapsed();
        (result, duration)
    }

    /// Measure execution time of a synchronous function
    pub fn measure_sync<T, F>(operation: F) -> (T, std::time::Duration)
    where
        F: FnOnce() -> T,
    {
        let start = Instant::now();
        let result = operation();
        let duration = start.elapsed();
        (result, duration)
    }

    /// Get age of a file or directory
    pub fn get_file_age(path: &std::path::Path) -> Result<std::time::Duration> {
        let metadata = std::fs::metadata(path)
            .context("Failed to get file metadata")?;

        let modified = metadata.modified()
            .context("Failed to get file modification time")?;

        let now = SystemTime::now();
        now.duration_since(modified)
            .context("File modification time is in the future")
    }

    /// Check if a file is older than a specified duration
    pub fn is_file_older_than(path: &std::path::Path, max_age: std::time::Duration) -> Result<bool> {
        let age = Self::get_file_age(path)?;
        Ok(age > max_age)
    }

    /// Get a human-readable relative time string
    pub fn relative_time(timestamp: DateTime<Utc>) -> String {
        let now = Utc::now();
        let duration = now.signed_duration_since(timestamp);

        if duration.num_seconds() < 60 {
            "just now".to_string()
        } else if duration.num_minutes() < 60 {
            format!("{} minutes ago", duration.num_minutes())
        } else if duration.num_hours() < 24 {
            format!("{} hours ago", duration.num_hours())
        } else if duration.num_days() < 7 {
            format!("{} days ago", duration.num_days())
        } else if duration.num_weeks() < 4 {
            format!("{} weeks ago", duration.num_weeks())
        } else {
            timestamp.format("%Y-%m-%d").to_string()
        }
    }

    /// Schedule a task to run at a specific time
    pub async fn schedule_at(target_time: DateTime<Utc>, task: impl std::future::Future<Output = ()>) {
        let now = Utc::now();
        if target_time > now {
            let delay = (target_time - now).to_std().unwrap_or_default();
            tokio::time::sleep(delay).await;
        }
        task.await;
    }

    /// Create a deadline for an operation
    pub fn deadline_from_now(duration: std::time::Duration) -> Instant {
        Instant::now() + duration
    }

    /// Check if a deadline has passed
    pub fn is_deadline_passed(deadline: Instant) -> bool {
        Instant::now() > deadline
    }

    /// Get remaining time until deadline
    pub fn time_until_deadline(deadline: Instant) -> Option<std::time::Duration> {
        if Self::is_deadline_passed(deadline) {
            None
        } else {
            Some(deadline - Instant::now())
        }
    }
}

/// A simple stopwatch for timing operations
#[derive(Debug)]
pub struct Stopwatch {
    start_time: Instant,
    lap_times: Vec<Instant>,
}

impl Stopwatch {
    /// Create a new stopwatch and start timing
    pub fn start() -> Self {
        Self {
            start_time: Instant::now(),
            lap_times: Vec::new(),
        }
    }

    /// Record a lap time
    pub fn lap(&mut self) -> std::time::Duration {
        let now = Instant::now();
        self.lap_times.push(now);
        now - self.start_time
    }

    /// Get elapsed time since start
    pub fn elapsed(&self) -> std::time::Duration {
        self.start_time.elapsed()
    }

    /// Get all lap times
    pub fn lap_times(&self) -> Vec<std::time::Duration> {
        self.lap_times
            .iter()
            .map(|&lap_time| lap_time - self.start_time)
            .collect()
    }

    /// Reset the stopwatch
    pub fn reset(&mut self) {
        self.start_time = Instant::now();
        self.lap_times.clear();
    }
}

impl Default for Stopwatch {
    fn default() -> Self {
        Self::start()
    }
}