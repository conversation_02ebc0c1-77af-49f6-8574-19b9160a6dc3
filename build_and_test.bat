@echo off
setlocal enabledelayedexpansion

:: Matrix AI Build and Test Script

echo.
echo 🔨 Matrix AI - Build and Test
echo ═══════════════════════════════
echo.

cd /d "%~dp0"
set "PATH=%USERPROFILE%\.cargo\bin;%PATH%"

echo 🔍 Checking environment...

:: Check Rust
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Rust/Cargo not found
    echo Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)
echo ✅ Rust/Cargo found

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo 🧪 Running Python tests...
python test_python_components.py
if %errorlevel% neq 0 (
    echo ❌ Python tests failed
    pause
    exit /b 1
)

echo.
echo 🔧 Checking Rust code...
cargo check
if %errorlevel% neq 0 (
    echo ❌ Rust check failed
    echo Please review the compilation errors above
    pause
    exit /b 1
)
echo ✅ Rust check passed

echo.
echo 🏗️ Building Rust components...
cargo build --release
if %errorlevel% neq 0 (
    echo ❌ Rust build failed
    pause
    exit /b 1
)
echo ✅ Rust build completed

echo.
echo 🧪 Running Rust tests...
cargo test
if %errorlevel% neq 0 (
    echo ⚠️  Some Rust tests failed (this may be normal for integration tests)
)

echo.
echo 🎉 Build and test completed!
echo.
echo Available executables:
if exist "target\release\matrix-ai.exe" (
    echo ✅ target\release\matrix-ai.exe
) else (
    echo ❌ matrix-ai.exe not found
)

echo.
echo 🚀 Ready to run Matrix AI!
echo Use: run_matrix_ai.bat
echo.

pause
