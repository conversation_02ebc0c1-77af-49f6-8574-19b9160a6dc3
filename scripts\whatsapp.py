#!/usr/bin/env python3
"""
Matrix AI - WhatsApp Automation Script
Automates WhatsApp Web for sending messages
"""

import sys
import time
import json
import argparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class WhatsAppAutomation:
    def __init__(self, headless=False, timeout=30):
        self.timeout = timeout
        self.driver = None
        self.wait = None
        self.setup_driver(headless)

    def setup_driver(self, headless):
        """Setup Chrome WebDriver"""
        chrome_options = Options()

        if headless:
            chrome_options.add_argument("--headless")

        # Add options for better compatibility
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # User data directory to persist login
        chrome_options.add_argument("--user-data-dir=./whatsapp_profile")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.timeout)
            print("✓ Chrome WebDriver initialized successfully")
        except Exception as e:
            print(f"✗ Failed to initialize WebDriver: {e}")
            sys.exit(1)

    def open_whatsapp_web(self):
        """Open WhatsApp Web"""
        try:
            print("Opening WhatsApp Web...")
            self.driver.get("https://web.whatsapp.com")

            # Wait for either QR code or chat list to load
            try:
                # Check if already logged in
                self.wait.until(
                    EC.any_of(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-ref]"))  # QR code
                    )
                )

                # Check if QR code is present
                qr_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-ref]")
                if qr_elements:
                    print("⚠ QR Code detected. Please scan the QR code to login.")
                    print("Waiting for login...")

                    # Wait for login to complete
                    self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='chat-list']"))
                    )

                print("✓ WhatsApp Web loaded successfully")
                return True

            except TimeoutException:
                print("✗ Timeout waiting for WhatsApp Web to load")
                return False

        except Exception as e:
            print(f"✗ Error opening WhatsApp Web: {e}")
            return False

    def find_contact(self, contact_name):
        """Find and click on a contact"""
        try:
            print(f"Searching for contact: {contact_name}")

            # Click on search box
            search_box = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='chat-list-search']"))
            )
            search_box.click()

            # Type contact name
            search_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='search-input']"))
            )
            search_input.clear()
            search_input.send_keys(contact_name)

            time.sleep(2)  # Wait for search results

            # Click on first search result
            contact_element = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='cell-frame-container']"))
            )
            contact_element.click()

            print(f"✓ Contact '{contact_name}' selected")
            return True

        except TimeoutException:
            print(f"✗ Contact '{contact_name}' not found")
            return False
        except Exception as e:
            print(f"✗ Error finding contact: {e}")
            return False

    def send_message(self, message):
        """Send a message to the selected contact"""
        try:
            print(f"Sending message: {message}")

            # Find message input box
            message_box = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='conversation-compose-box-input']"))
            )

            # Type message
            message_box.click()
            message_box.send_keys(message)

            # Send message
            send_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "[data-testid='send']"))
            )
            send_button.click()

            print("✓ Message sent successfully")
            return True

        except TimeoutException:
            print("✗ Timeout sending message")
            return False
        except Exception as e:
            print(f"✗ Error sending message: {e}")
            return False

    def send_whatsapp_message(self, contact_name, message):
        """Complete flow to send a WhatsApp message"""
        try:
            if not self.open_whatsapp_web():
                return False

            if not self.find_contact(contact_name):
                return False

            if not self.send_message(message):
                return False

            print("✓ WhatsApp message sent successfully!")
            return True

        except Exception as e:
            print(f"✗ Error in send_whatsapp_message: {e}")
            return False

    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            print("✓ WebDriver closed")

def main():
    parser = argparse.ArgumentParser(description="Matrix AI WhatsApp Automation")
    parser.add_argument("--contact", required=True, help="Contact name to send message to")
    parser.add_argument("--message", required=True, help="Message to send")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    parser.add_argument("--timeout", type=int, default=30, help="Timeout in seconds")

    args = parser.parse_args()

    print("🤖 Matrix AI WhatsApp Automation")
    print("=" * 40)

    automation = WhatsAppAutomation(headless=args.headless, timeout=args.timeout)

    try:
        success = automation.send_whatsapp_message(args.contact, args.message)

        if success:
            print("\n✅ Operation completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Operation failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        automation.close()

if __name__ == "__main__":
    main()