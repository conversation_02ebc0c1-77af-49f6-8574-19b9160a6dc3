use crate::prelude::*;
use regex::Regex;
use std::collections::HashMap;

/// Represents different types of intents that can be parsed from LLM responses
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Intent {
    OpenApp {
        app_name: String,
        details: Option<String>,
    },
    SendMessage {
        platform: MessagePlatform,
        target: String,
        message: String,
    },
    SearchWeb {
        query: String,
        search_engine: Option<String>,
    },
    SystemControl {
        action: SystemAction,
        parameters: Option<String>,
    },
    BrowserControl {
        action: BrowserAction,
        url: Option<String>,
        text: Option<String>,
    },
    EmailAction {
        action: EmailActionType,
        recipient: Option<String>,
        subject: Option<String>,
        body: Option<String>,
    },
    Unknown {
        original_text: String,
        suggested_response: String,
    },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MessagePlatform {
    WhatsApp,
    Email,
    SMS,
}

#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum SystemAction {
    VolumeUp,
    VolumeDown,
    Mute,
    Screenshot,
    Shutdown,
    Restart,
    Sleep,
    Lock,
    Scroll(ScrollDirection),
}

#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize)]
pub enum ScrollDirection {
    Up,
    Down,
    Left,
    Right,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BrowserAction {
    Open,
    Search,
    Navigate,
    ReadText,
    Click,
    Type,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EmailActionType {
    Send,
    Read,
    Reply,
    Forward,
}

/// Parser for extracting intent from LLM responses
pub struct IntentParser {
    intent_regex: Regex,
    target_regex: Regex,
    details_regex: Regex,
    response_regex: Regex,
}

impl IntentParser {
    /// Create a new intent parser
    pub fn new() -> Self {
        Self {
            intent_regex: Regex::new(r"INTENT:\s*(.+?)(?:\n|$)").unwrap(),
            target_regex: Regex::new(r"TARGET:\s*(.+?)(?:\n|$)").unwrap(),
            details_regex: Regex::new(r"DETAILS:\s*(.+?)(?:\n|$)").unwrap(),
            response_regex: Regex::new(r"RESPONSE:\s*(.+?)(?:\n|$)").unwrap(),
        }
    }

    /// Parse intent from LLM response text
    pub fn parse(&self, llm_response: &str) -> Result<Intent> {
        let intent_str = self.extract_field(&self.intent_regex, llm_response)
            .unwrap_or_else(|| "unknown".to_string());

        let target = self.extract_field(&self.target_regex, llm_response);
        let details = self.extract_field(&self.details_regex, llm_response);
        let response = self.extract_field(&self.response_regex, llm_response)
            .unwrap_or_else(|| "I'll help you with that.".to_string());

        debug!("Parsing intent: {} | target: {:?} | details: {:?}", intent_str, target, details);

        let intent = match intent_str.to_lowercase().as_str() {
            "open_app" | "launch_app" | "start_app" => {
                if let Some(app_name) = target {
                    Intent::OpenApp {
                        app_name: app_name.to_lowercase(),
                        details,
                    }
                } else {
                    Intent::Unknown {
                        original_text: llm_response.to_string(),
                        suggested_response: "I need to know which app to open.".to_string(),
                    }
                }
            }

            "send_message" | "send_whatsapp" | "message" => {
                if let (Some(target), Some(message)) = (target, details) {
                    Intent::SendMessage {
                        platform: MessagePlatform::WhatsApp,
                        target: target.to_string(),
                        message: message.to_string(),
                    }
                } else {
                    Intent::Unknown {
                        original_text: llm_response.to_string(),
                        suggested_response: "I need to know who to message and what to say.".to_string(),
                    }
                }
            }

            "search_web" | "search" | "google" | "web_search" => {
                if let Some(query) = target.or(details) {
                    Intent::SearchWeb {
                        query: query.to_string(),
                        search_engine: Some("google".to_string()),
                    }
                } else {
                    Intent::Unknown {
                        original_text: llm_response.to_string(),
                        suggested_response: "What would you like me to search for?".to_string(),
                    }
                }
            }

            "system_control" | "system" => {
                self.parse_system_action(&target, &details)
            }

            "browser_control" | "browser" => {
                self.parse_browser_action(&target, &details)
            }

            "email" | "send_email" => {
                self.parse_email_action(&target, &details)
            }

            _ => {
                Intent::Unknown {
                    original_text: llm_response.to_string(),
                    suggested_response: response,
                }
            }
        };

        Ok(intent)
    }

    /// Extract a field using regex
    fn extract_field(&self, regex: &Regex, text: &str) -> Option<String> {
        regex.captures(text)
            .and_then(|caps| caps.get(1))
            .map(|m| m.as_str().trim().to_string())
    }

    /// Parse system control actions
    fn parse_system_action(&self, target: &Option<String>, details: &Option<String>) -> Intent {
        let action_str = target.as_ref()
            .or(details.as_ref())
            .map(|s| s.to_lowercase())
            .unwrap_or_default();

        let action = match action_str.as_str() {
            "volume_up" | "volume up" | "louder" => SystemAction::VolumeUp,
            "volume_down" | "volume down" | "quieter" => SystemAction::VolumeDown,
            "mute" | "silence" => SystemAction::Mute,
            "screenshot" | "screen capture" => SystemAction::Screenshot,
            "shutdown" | "power off" => SystemAction::Shutdown,
            "restart" | "reboot" => SystemAction::Restart,
            "sleep" | "suspend" => SystemAction::Sleep,
            "lock" | "lock screen" => SystemAction::Lock,
            "scroll up" => SystemAction::Scroll(ScrollDirection::Up),
            "scroll down" => SystemAction::Scroll(ScrollDirection::Down),
            "scroll left" => SystemAction::Scroll(ScrollDirection::Left),
            "scroll right" => SystemAction::Scroll(ScrollDirection::Right),
            _ => {
                return Intent::Unknown {
                    original_text: format!("system_control: {}", action_str),
                    suggested_response: "I'm not sure what system action you want me to perform.".to_string(),
                };
            }
        };

        Intent::SystemControl {
            action,
            parameters: details.clone(),
        }
    }

    /// Parse browser control actions
    fn parse_browser_action(&self, target: &Option<String>, details: &Option<String>) -> Intent {
        let action_str = target.as_ref()
            .map(|s| s.to_lowercase())
            .unwrap_or_default();

        let action = match action_str.as_str() {
            "open" | "navigate" => BrowserAction::Open,
            "search" => BrowserAction::Search,
            "read" | "read_text" => BrowserAction::ReadText,
            "click" => BrowserAction::Click,
            "type" | "input" => BrowserAction::Type,
            _ => BrowserAction::Open,
        };

        Intent::BrowserControl {
            action,
            url: details.clone(),
            text: details.clone(),
        }
    }

    /// Parse email actions
    fn parse_email_action(&self, target: &Option<String>, details: &Option<String>) -> Intent {
        let action = EmailActionType::Send; // Default to send for now

        Intent::EmailAction {
            action,
            recipient: target.clone(),
            subject: None,
            body: details.clone(),
        }
    }

    /// Parse intent from natural language (fallback method)
    pub fn parse_natural_language(&self, text: &str) -> Intent {
        let text_lower = text.to_lowercase();

        // Simple keyword-based parsing as fallback
        if text_lower.contains("open") || text_lower.contains("launch") || text_lower.contains("start") {
            if let Some(app_name) = self.extract_app_name(&text_lower) {
                return Intent::OpenApp {
                    app_name,
                    details: None,
                };
            }
        }

        if text_lower.contains("message") || text_lower.contains("whatsapp") || text_lower.contains("send") {
            return Intent::SendMessage {
                platform: MessagePlatform::WhatsApp,
                target: "unknown".to_string(),
                message: text.to_string(),
            };
        }

        if text_lower.contains("search") || text_lower.contains("google") || text_lower.contains("find") {
            return Intent::SearchWeb {
                query: text.to_string(),
                search_engine: Some("google".to_string()),
            };
        }

        Intent::Unknown {
            original_text: text.to_string(),
            suggested_response: "I'm not sure what you want me to do. Can you please rephrase?".to_string(),
        }
    }

    /// Extract app name from text
    fn extract_app_name(&self, text: &str) -> Option<String> {
        let common_apps = vec![
            "chrome", "firefox", "edge", "safari",
            "notepad", "calculator", "paint",
            "spotify", "discord", "slack", "teams",
            "word", "excel", "powerpoint",
            "vscode", "visual studio code",
        ];

        for app in common_apps {
            if text.contains(app) {
                return Some(app.to_string());
            }
        }

        None
    }
}