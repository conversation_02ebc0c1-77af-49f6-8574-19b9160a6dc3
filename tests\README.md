# Matrix AI Tests

This directory contains tests for Matrix AI components.

## Running Tests

### All Tests
```bash
cargo test
```

### Integration Tests Only
```bash
cargo test --test integration_test
```

### Unit Tests Only
```bash
cargo test --lib
```

### With Output
```bash
cargo test -- --nocapture
```

## Test Categories

### Unit Tests
- Configuration loading and validation
- Intent parsing logic
- Audio processing utilities
- Time utilities
- Basic component initialization

### Integration Tests
- End-to-end workflow testing
- Component interaction testing
- Configuration serialization/deserialization

## Test Requirements

Some tests may require external dependencies:
- Ollama server running (for LLM tests)
- Audio devices available (for audio tests)
- Network connectivity (for web automation tests)

Tests are designed to gracefully handle missing dependencies and provide informative error messages.

## Adding New Tests

When adding new functionality to Matrix AI, please include corresponding tests:

1. Unit tests for individual functions
2. Integration tests for component interactions
3. End-to-end tests for complete workflows

## Test Data

Test data and fixtures should be placed in the `tests/fixtures/` directory (create if needed).

## Continuous Integration

These tests are designed to run in CI environments and will skip tests that require unavailable resources.
