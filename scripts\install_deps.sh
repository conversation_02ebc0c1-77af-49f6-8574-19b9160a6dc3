#!/bin/bash

# Matrix AI - Autonomous Installation Script
# This script sets up Matrix AI and all its dependencies offline

set -e  # Exit on any error

echo "╔══════════════════════════════════════╗"
echo "║        Matrix AI Installation        ║"
echo "║     Autonomous Setup Script v1.0     ║"
echo "╚══════════════════════════════════════╝"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_system_requirements() {
    print_status "Checking system requirements..."

    # Check operating system
    OS=$(uname -s)
    print_status "Operating System: $OS"

    # Check architecture
    ARCH=$(uname -m)
    print_status "Architecture: $ARCH"

    # Check available memory
    if command_exists free; then
        MEMORY=$(free -h | awk '/^Mem:/ {print $2}')
        print_status "Available Memory: $MEMORY"
    fi

    # Check disk space
    DISK_SPACE=$(df -h . | awk 'NR==2 {print $4}')
    print_status "Available Disk Space: $DISK_SPACE"
}

# Function to check for required tools
check_required_tools() {
    print_status "Checking for required development tools..."

    local missing_tools=()

    # Check for Rust/Cargo
    if ! command_exists cargo; then
        missing_tools+=("Rust/Cargo")
        print_warning "Rust/Cargo not found"
    else
        RUST_VERSION=$(rustc --version)
        print_success "Found: $RUST_VERSION"
    fi

    # Check for Python
    if ! command_exists python3; then
        missing_tools+=("Python 3")
        print_warning "Python 3 not found"
    else
        PYTHON_VERSION=$(python3 --version)
        print_success "Found: $PYTHON_VERSION"
    fi

    # Check for Node.js (optional for UI)
    if ! command_exists node; then
        print_warning "Node.js not found (optional for UI)"
    else
        NODE_VERSION=$(node --version)
        print_success "Found: Node.js $NODE_VERSION"
    fi

    # Check for Git
    if ! command_exists git; then
        print_warning "Git not found (recommended)"
    else
        GIT_VERSION=$(git --version)
        print_success "Found: $GIT_VERSION"
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install the missing tools manually and run this script again."
        print_status "Installation instructions:"
        print_status "- Rust: https://rustup.rs/"
        print_status "- Python 3: https://www.python.org/downloads/"
        print_status "- Node.js: https://nodejs.org/ (optional)"
        exit 1
    fi
}

# Function to create directory structure
create_directories() {
    print_status "Creating Matrix AI directory structure..."

    local dirs=(
        "models/stt"
        "models/tts"
        "logs"
        "screenshots"
        "scripts/offline_wheels"
        "whisper.cpp"
        "piper"
    )

    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_status "Directory already exists: $dir"
        fi
    done
}

# Function to check for AI models
check_ai_models() {
    print_status "Checking for AI models..."

    # Check for Whisper model
    if [ ! -f "models/stt/ggml-base.en.bin" ]; then
        print_warning "Whisper STT model not found at models/stt/ggml-base.en.bin"
        print_status "Please download the Whisper model manually:"
        print_status "wget https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.en.bin -O models/stt/ggml-base.en.bin"
    else
        print_success "Found Whisper STT model"
    fi

    # Check for Piper TTS model
    if [ ! -f "models/tts/en_US-amy-low.onnx" ]; then
        print_warning "Piper TTS model not found at models/tts/en_US-amy-low.onnx"
        print_status "Please download the Piper model manually from:"
        print_status "https://github.com/rhasspy/piper/releases"
    else
        print_success "Found Piper TTS model"
    fi
}

# Function to check for external tools
check_external_tools() {
    print_status "Checking for external AI tools..."

    # Check for Ollama
    if ! command_exists ollama; then
        print_warning "Ollama not found"
        print_status "Please install Ollama manually:"
        print_status "curl -fsSL https://ollama.ai/install.sh | sh"
    else
        print_success "Found Ollama"

        # Check if TinyLlama model is available
        if ollama list | grep -q "tinyllama"; then
            print_success "TinyLlama model is available"
        else
            print_warning "TinyLlama model not found"
            print_status "Run: ollama pull tinyllama"
        fi
    fi

    # Check for Whisper.cpp
    if [ ! -f "whisper.cpp/main" ] && [ ! -f "whisper.cpp/main.exe" ]; then
        print_warning "Whisper.cpp executable not found"
        print_status "Please build Whisper.cpp manually in the whisper.cpp/ directory"
    else
        print_success "Found Whisper.cpp executable"
    fi

    # Check for Piper
    if [ ! -f "piper/piper" ] && [ ! -f "piper/piper.exe" ]; then
        print_warning "Piper TTS executable not found"
        print_status "Please download Piper binary to the piper/ directory"
    else
        print_success "Found Piper TTS executable"
    fi
}

# Function to install Python dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."

    if [ -f "scripts/requirements.txt" ]; then
        if [ -d "scripts/offline_wheels" ] && [ "$(ls -A scripts/offline_wheels)" ]; then
            print_status "Installing from offline wheels..."
            python3 -m pip install --no-index --find-links=scripts/offline_wheels -r scripts/requirements.txt
        else
            print_status "Installing from PyPI..."
            python3 -m pip install -r scripts/requirements.txt
        fi
        print_success "Python dependencies installed"
    else
        print_warning "requirements.txt not found, skipping Python dependencies"
    fi
}

# Function to build Matrix AI
build_matrix_ai() {
    print_status "Building Matrix AI..."

    if [ -f "Cargo.toml" ]; then
        print_status "Compiling Rust code..."
        cargo build --release

        if [ $? -eq 0 ]; then
            print_success "Matrix AI built successfully!"
            print_success "Binary location: ./target/release/matrix-ai"
        else
            print_error "Failed to build Matrix AI"
            exit 1
        fi
    else
        print_error "Cargo.toml not found. Are you in the Matrix AI directory?"
        exit 1
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."

    if cargo test; then
        print_success "All tests passed!"
    else
        print_warning "Some tests failed, but Matrix AI should still work"
    fi
}

# Function to create configuration
create_config() {
    print_status "Creating default configuration..."

    if [ ! -f "$HOME/.matrix-ai/config.json" ]; then
        mkdir -p "$HOME/.matrix-ai"
        print_status "Configuration will be created on first run"
    else
        print_status "Configuration already exists"
    fi
}

# Main installation function
main() {
    print_status "Starting Matrix AI installation..."

    check_system_requirements
    check_required_tools
    create_directories
    check_ai_models
    check_external_tools
    install_python_deps
    build_matrix_ai
    run_tests
    create_config

    echo ""
    print_success "Matrix AI installation completed!"
    echo ""
    print_status "Next steps:"
    print_status "1. Download missing AI models if any"
    print_status "2. Install Ollama and pull tinyllama model"
    print_status "3. Run: ./target/release/matrix-ai"
    echo ""
    print_status "For help, check the README.md file"
    echo ""
    echo "╔══════════════════════════════════════╗"
    echo "║     Matrix AI Ready for Launch!      ║"
    echo "╚══════════════════════════════════════╝"
}

# Run main function
main "$@"